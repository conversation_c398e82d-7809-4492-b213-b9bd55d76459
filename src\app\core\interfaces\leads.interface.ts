import {
  EnquiryType,
  LeadAssignmentType,
  SaleType,
} from 'src/app/app.enum';
import { Entity } from 'src/app/core/interfaces/common.interface';

export interface Lead extends Entity {
  name: string;
  contactNo: string;
  alternateContactNo: string;
  email: string;
  notes: string;
  scheduledDate: string | null;
  bookedDate: string | null;
  revertDate: string | null;
  chosenProject: string;
  chosenProperty: string;
  bookedUnderName: string;
  leadNumber: string;
  shareCount: number;
  soldPrice: string;
  rating: string;
  leadTagInfo: LeadTagInfo;
  assignTo: string;
  lastModifiedOn: string;
  pickedDate: string;
  createdOn: string;
  createdBy: string;
  lastModifiedBy: string;
  status: LeadStatusType;
  enquiry: LeadEnquiryInfo[];
  documents: Document[];
  projectIds: string[];
  propertyIds: string[];
  projects: Project[];
  properties: Property[];
  isPicked: boolean;
}

export interface LeadTagInfo {
  id: string;
  isHighlighted: boolean;
  isEscalated: boolean;
  isAboutToConvert: boolean;
  isHotLead: boolean;
  isIntegrationLead: boolean;
}

export interface LeadStatusType {
  id: string;
  baseId: string;
  level: number;
  status: string;
  displayName: string;
  actionName: string;
  childType: MasterPropertyTypeChild;
}

export interface LeadEnquiryInfo {
  id: string;
  enquiredFor: EnquiryType;
  saleType: SaleType;
  leadSource: any; // LeadSource enum removed - using dynamic sources
  lowerBudget: number;
  upperBudget: number;
  noOfBHK: number;
  bhkType: number;
  area: number;
  areaUnitId: string;
  areaUnit: string;
  isPrimary: boolean;
  address: LeadAddress;
  propertyType: PropertyInfoType;
  propertyDimensionInfo: PropertyDimensionInfo;
}

export interface Document {
  documentName: string;
  filePath: string;
}

export interface Project {
  id: string;
  name: string;
}

export interface Property {
  id: string;
  name: string;
}

export interface PropertyInfoType {
  id: string;
  displayName: string;
  noOfBHK: number;
  childType: MasterPropertyTypeChild;
}

export interface MasterPropertyTypeChild {
  id: string;
  displayName: string;
}

export interface LeadAddress {
  id: string;
  placeId: string;
  subLocality: string;
  locality: string;
  district: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
  longitude: string;
  latitude: string;
  isGoogleMapLocation: boolean;
}

export interface PropertyDimensionInfo {
  id: string;
  area: number;
  areaUnit: string;
  areaInSqMtr: number;
}

export interface MasterLeadStatusTypeChild {
  id: string;
  displayName: string;
  actionName: string;
}

export interface LeadsFilter {
  // UserType: any;
  secondLevelFilterId: any;
  pageNumber?: number;
  pageSize?: number;
  enquiredFor?: string;
  leadVisibility?: string;
  dateType?: string;
  fromDate?: any;
  toDate?: any;
  path: string;
  assignTo?: [];
  SecondaryUsers?: [];
  AssignedFromIds?: [];
  createdByIds?: [];
  lastModifiedByIds?: [];
  archivedByIds?: [];
  restoredByIds?: [];
  noOfRecords?: number;
  LeadTags?: [];
  BHKTypes?: [];
  MinBudget?: number;
  MaxBudget?: number;
  Currency?: string;
  Source?: [];
  SubSources?: [];
  NoOfBHKs?: [];
  Beds?: [];
  Baths?: [];
  Furnished?: [];
  Floors?: [];
  OfferTypes?: [];
  Properties?: [];
  UploadTypeName?: [];
  IsPicked?: boolean;
  IsUntouched?: boolean;
  Projects?: [];
  PropertyType?: [];
  PropertySubType?: [];
  MeetingOrVisitStatuses?: string[];
  SearchByNameOrNumber?: string;
  LeadSearch?: string;
  Locations?: string[];
  Cities?: string[];
  States?: string[];
  Countries?: string[];
  SubCommunities?: string[];
  Communities?: string[];
  TowerNames?: string[];
  Localities?: string[];
  Latitude?: number;
  Longitude?: number;
  RadiusInKm?: number;
  Zones?: string[];
  FromDateForMeetingOrVisit?: any;
  ToDateForMeetingOrVisit?: any;
  AppointmentDoneByUserIds: [];
  AgencyNames: string[];
  CampaignNames: string[];
  IsWithTeam: boolean;
  StatusIds: [];
  SubStatusIds: [];
  FirstLevelFilter: string | number;
  SecondLevelFilter: string;
  ScheduledDateTypeFilter: string;
  ScheduledType: string;
  CarpetArea?: number;
  CarpetAreaUnitId?: string;
  SaleableArea?: number;
  SaleableAreaUnitId?: string;
  BuiltUpArea?: number;
  BuiltUpAreaUnitId?: string
  ReferralContactNo?: [];
  ReferralName?: [];
  ReferralEmail?: [];
  CompanyName?: [];
  closingManagers?: string;
  sourcingManagers?: string;
  Profession?: number[];
  channelPartnerExecutiveName?: string;
  channelPartnerContactNo?: string;
  ChannelPartnerNames?: string[];
  IsOnlyReportees: boolean;
  'SortingCriteria.ColumnName': string;
  'SortingCriteria.IsAscending': boolean;
  Designation?: string[];
  IsDualOwnershipEnabled: boolean;
  bookedByIds?: [];
  designationsId?: [];
  duplicateLeadIds: any;
  shouldShowParentLead: any;
  customFlags: any[];
  shouldShowBookedDetails: boolean;
  shouldShowBrokerageInfo: boolean;
  BookedUnderName: string;
  LowerAgreementLimit: number;
  UpperAgreementLimit: number;
  carParkingCharges: number;
  AdditionalCharges: number;
  SoldPrice: number;
  paymentMode: any;
  LowerDiscountLimit: number;
  UpperDiscountLimit: number;
  RemainingAmount: number;
  PaymentType: any;
  BrokerageCharges: number;
  NetBrokerageAmount: number;
  gst: number;
  TotalBrokerage: number;
  EarnedBrokerage: number;
  CustomFilterId: string;
  customFirstLevelFilterId: string;
  customSecondLevelFilterId: string;
  customThirdLevelFilterId: string;
  customFirstLevelFilter: string;
  customSecondLevelFilter: string;
  customThirdLevelFilter: string;
  customFourthLevelFilter: string;
  HistoryAssignedToIds: any[];
  SecondaryFromIds: any[];
  DoneBy: any[];
  IsWithHistory: boolean;
  AdditionalPropertiesKey?: any;
  AdditionalPropertiesValue?: any;
  CanAccessAllLeads: boolean;
  DataConverted: any;
  QualifiedByIds: any
}

export interface LeadPayload {
  id: string;
  name: string;
  contactNo: string;
  email: string;
  notes: string;
  scheduledDate: string;
  bookedDate: string;
  revertDate: string;
  chosenProperty: string;
  chosenProject: string;
  bookedUnderName: string;
  leadNumber: string;
  shareCount: number;
  soldPrice: string;
  rating: string;
  leadTags: LeadTagInfo;
  assignTo: string;
  leadStatusId: string;
  enquiry: LeadEnquiryInfo;
  address: LeadAddress;
  propertytypeId: string;
}

export interface SuccessLeadUpdate {
  id: string;
  leadStatusId: string;
  notes: string;
  scheduledDate: string;
  bookedDate: string;
  revertDate?: String;
  rating: String;
}

export interface LeadNote {
  id: string;
  notes: string;
}

export interface AssignLead {
  leadIds: string[];
  userIds?: string[];
}

export interface BulkAssignLead {
  leadIds: string[];
  assignmentType: LeadAssignmentType;
  updateSource: boolean;
  leadSource: any; // LeadSource enum removed - using dynamic sources
  updateSubSource: boolean;
  subSource: string;
  createDuplicate: boolean;
  userIds: string[];
  updateProject: boolean;
  projects: string[];
  bulkCategory: number;
}

export interface LeadExcelData {
  s3BucketKey: string;
  columnNames: string[];
}

export interface LeadExcel {
  succeeded: boolean;
  message: string;
  errors: string[];
  data: LeadExcelData;
}

export interface MapColumnsData {
  additionalProp1: string;
  additionalProp2: string;
  additionalProp3: string;
}

export interface MapColumnsExcel {
  s3BucketKey: string;
  mappedColumnData: MapColumnsData;
  userIds: string[];
}

export interface UpdateTags {
  id: string;
  flagId: string;
}

export interface MultipleLeads {
  requests: SuccessLeadUpdate[];
}

export interface WAWrapperDto {
  message?: string;
  templateName?: string;
  customerId?: string;
  customerNo?: string;
  userId?: string;
  tenantId?: string;
  messageId?: string;
  waPayloadMapping?: WAPayloadMappingDto;
  waApiInfo?: WAApiInfoDto;
  mediaURL?: string;
  mediaType?: string;
  mediaName?: string;
  wAButtons?: Array<any>;
}

export interface WAApiInfoDto {
  id: string;
  url?: string;
  methodType?: string;
  headers?: { [key: string]: string };
  shouldUseForTextMessaging: boolean;
  jsonPayload?: string;
}

export interface WAPayloadMappingDto {
  serviceProviderName?: string;
  statusMapping?: { [key: string]: string };
  webhookMapping?: { [key: string]: string };
  responseMapping?: { [key: string]: string };
  outboundMapping: { [key: string]: string };
}
