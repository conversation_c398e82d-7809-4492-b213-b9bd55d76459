import { EntityBase } from './common.interface';

export interface Source extends EntityBase {
  displayName: string;
  value: number;
  imageURL: string;
  isEnabled: boolean;
  isMasterSource: boolean;
  enabledFor: 'Lead' | 'Data' | 'Both';
  subSources: string[];
}

export interface SourcePayload {
  sourceName: string;
  subSources: string[];
  enabledFor: 'Lead' | 'Data' | 'Both';
  logoFile?: File | null;
}

export interface SourceUpdatePayload extends SourcePayload {
  id: string;
}

export interface SourceCountData {
  leadCount: number;
  prospectCount: number;
}

export interface SourceAccount {
  id: string;
  sourceId: string;
  accountName: string;
  loginId?: string;
  loginEmail?: string;
  relationshipManagerEmail: string;
  additionalEmail?: string;
  bccEmail?: string;
  integrationMethod: 'PUSH' | 'PULL';
  webhookUrl?: string;
  curlCommand?: string;
  payloadMapping: PayloadMapping[];
  methodType?: 'GET' | 'POST';
  contentType?: 'form-data' | 'x-www-form-urlencoded' | 'application/json';
  queryParameters?: KeyValuePair[];
  headerVariables?: KeyValuePair[];
  bodyVariables?: KeyValuePair[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface PayloadMapping {
  sourceField: string;
  targetField: string;
  isRequired: boolean;
  isPreSelected?: boolean;
}

export interface KeyValuePair {
  key: string;
  value: string;
}

export interface SourceAccountPayload {
  sourceId: string;
  accountName: string;
  loginId?: string;
  loginEmail?: string;
  relationshipManagerEmail: string;
  additionalEmail?: string;
  bccEmail?: string;
  integrationMethod: 'PUSH' | 'PULL';
  webhookUrl?: string;
  curlCommand?: string;
  payloadMapping: PayloadMapping[];
  methodType?: 'GET' | 'POST';
  contentType?: 'form-data' | 'x-www-form-urlencoded' | 'application/json';
  queryParameters?: KeyValuePair[];
  headerVariables?: KeyValuePair[];
  bodyVariables?: KeyValuePair[];
}

export interface ExtractedFields {
  fields: string[];
  samplePayload?: any;
}
