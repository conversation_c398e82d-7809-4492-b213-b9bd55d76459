import { Component, EventEmitter, Input, Output } from '@angular/core';
import { ThirdPartyIntegrationComponent } from '../integration/third-party-integration/third-party-integration.component';
import { BsModalService } from 'ngx-bootstrap/modal';
import { CommonFloorComponent } from '../integration/common-floor/common-floor.component';
import { Router } from '@angular/router';

@Component({
  selector: 'integration-card',
  templateUrl: './integration-card.component.html'
})
export class IntegrationCardComponent {
  @Input() isSkeleton: boolean = false;
  @Input() integration: {
    displayName: string;
    name: string;
    image: string;
    logo: string;
    description: string;
    isPushPullEnabled?: boolean;
  };
  @Input() functionType: string;
  @Output() connectNowClicked: EventEmitter<void> = new EventEmitter<void>();

  constructor(
    private modalService: BsModalService,
    private router: Router,

  ) { }


  // openIntegration(image: string, displayName: string, name: string) {  
  //   if (this.functionType) {
  //     this.connectNowClicked?.emit();
  //     return;
  //   }
  
  //   this.router.navigate([`/global-config/${displayName}`], {
  //     state: {
  //       image: image,
  //       displayName: displayName,
  //       name: name
  //     }
  //   });
    
  // }
  openIntegration(image: string, displayName: string, name: string) {
    if (this.functionType) {
      this.connectNowClicked?.emit();
      return;
    }

    // Check if push/pull is enabled for this integration
    const isPushPullEnabled = this.integration?.isPushPullEnabled || false;

    localStorage.setItem('integrationData', JSON.stringify({
      image,
      displayName,
      name,
      isPushPullEnabled
    }));

    // Navigate to v2 component if push/pull is enabled, otherwise use original component
    const route = isPushPullEnabled ? '/global-config/integration-v2' : '/global-config/integration';
    this.router.navigate([route]);
  }

  // openCommonFloorIntegration(
  //   image: string,
  //   displayName: string,
  //   name: string,
  //   count: string
  // ) {  
  //   if (this.functionType) {
  //     this.connectNowClicked?.emit();
  //     return;
  //   }
  
  //   localStorage.setItem('integrationData', JSON.stringify({ image, displayName, name, count }));
  
  //   this.router.navigate([`/global-config/integrations`]);
  // }

}
