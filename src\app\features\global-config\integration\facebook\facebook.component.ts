import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  OnDestroy,
  OnInit,
  TemplateRef,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { GridApi, GridOptions } from 'ag-grid-community';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { SHOW_ENTRIES } from 'src/app/app.constants';
// LeadSource enum removed - using dynamic sources instead
import { DATE_FILTER_LIST, VALIDATION_CLEAR, VALIDATION_SET } from 'src/app/app.constants';
import { DateRange } from 'src/app/app.enum';

import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  changeCalendar,
  getAppName,
  getDateRange,
  getEnvDetails,
  getTenantName,
  onFilterChanged,
  onPickerOpened,
  setTimeZoneDate,
  toggleValidation,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { BulkFetchFBLeads } from 'src/app/features/global-config/integration/facebook/bulk-fetch-fb-leads/bulk-fetch-fb-leads.component';
import { FacebookActionsComponent } from 'src/app/features/global-config/integration/facebook/facebook-actions/facebook-actions.component';
import { FbBulkAgencyComponent } from 'src/app/features/global-config/integration/facebook/fb-bulk-agency/fb-bulk-agency.component';
import { FbBulkCountryCodeUpdateComponent } from 'src/app/features/global-config/integration/facebook/fb-bulk-country-code-update/fb-bulk-country-code-update.component';
import { FbBulkLocationUpdateComponent } from 'src/app/features/global-config/integration/facebook/fb-bulk-location-update/fb-bulk-location-update.component';
import { FbBulkProjectUpdateComponent } from 'src/app/features/global-config/integration/facebook/fb-bulk-project-update/fb-bulk-project-update.component';
import { FetchPriorityList } from 'src/app/reducers/automation/automation.actions';
import { getPriorityList } from 'src/app/reducers/automation/automation.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  DeleteFacebookIntegration,
  FetchAgencyNameList,
  FetchExportFacebookStatus,
  FetchFacebookMarketing,
  FetchFbAccountFormsSuccess,
  SyncAdsOfAnFBAccount,
  UpdatePixelAccount
} from 'src/app/reducers/Integration/integration.actions';
import {
  fetchFbAccountForms,
  fetchFbAccountFormsIsLoading,
  getFacebookMarketing,
  getFacebookMarketingIsLoading
} from 'src/app/reducers/Integration/integration.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { getCustomStatusList } from 'src/app/reducers/status/status.reducer';
import {
  FetchAdminsAndReportees,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getAdminsAndReportees,
  getUserBasicDetails,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';
import { IntegrationService } from 'src/app/services/controllers/integration.service';
import { CommonService } from 'src/app/services/shared/common.service';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { ExportFacebookTrackerComponent } from 'src/app/shared/components/export-facebook-tracker/export-facebook-tracker.component';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { IntegrationAssignmentV2Component } from '../integration-assignment-v2/integration-assignment-v2.component';


@Component({
  selector: 'facebook',
  templateUrl: './facebook.component.html',
})
export class FacebookComponent implements OnInit, OnDestroy, AfterViewInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  assignedUser: any[] = [];
  assignedDuplicateUser: any[] = [];
  accounts: any[] = [];
  canAdd: boolean = false;
  canDelete: boolean = false;
  canView: boolean = false;
  canAssign: boolean = false;
  canAssignToAny: boolean = false;
  selectedAccountId: string = '';
  userList: any;
  allUserList: any;
  activeUsers: any;
  allActiveUsers: any;
  assignedUserDetails: Array<string> = [];
  hasAds: boolean = false;
  isAdsExpanded: boolean = false;
  isFormsExpanded: boolean = false;
  isAdsLoading: boolean = false;
  isFormsLoading: boolean = false;
  isAdAccount: boolean = false;
  isFormAccount: boolean = false;
  subscribedAds: Array<any> = [];
  selectedAccountName: any;
  selectedAdName: any;
  filteredAccounts: any[] = [];
  searchText: string = '';
  searchTerm: string = ''; // For ads search
  formsSearchTerm: string = ''; // For forms search
  source: any; // Using dynamic source from API
  moduleId: string;
  // account: any;
  // selectedAdsMap: Map<string, Set<string>> = new Map();
  // selectedFormsMap: Map<string, Set<string>> = new Map();
  initialState: any;
  gridOptions: GridOptions;
  account: any[] = [];
  gridColumnApi: any;
  gridApi: GridApi;
  filtersPayload: any;
  currOffset: number = 0;
  currentPageNumber: any;
  gridOptionsExternal: GridOptions<any>;
  gridOptionsInternal: GridOptions<any>;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  adsShowEntriesSize: Array<number> = SHOW_ENTRIES?.filter((item: any) => item <= 100);

  allAccounts: any[] = [];

  // Pagination for Ads
  adsPageSize: number = 10;
  adsPageNumber: number = 1;
  adsTotalCount: number = 0;

  // Pagination for Forms
  formsPageSize: number = 10;
  formsPageNumber: number = 1;
  formsTotalCount: number = 0;

  // Selected account for pagination
  selectedAccountForAds: any = null;
  selectedAccountForForms: any = null;
  rowData: any[];
  isManualChange: boolean = true;
  lastClickedOption: any;
  cities: any = [];
  canAllowDuplicates: boolean = false;
  canEnableAllowDuplicates: boolean = false;
  canAllowSecondaryUsers: boolean = false;
  message: string = '';
  notes: string = '';
  canEnableAllowSecondaryUsers: boolean = false;
  assignedSecondaryUsers: any[] = [];
  assignedPrimaryUsers: any[] = [];
  sameAsPrimaryUsers: boolean = false;
  sameAsSelectedUsers: boolean = false;
  sameAsAbove: boolean = false;
  image: string = '../../../../assets/images/integration/facebook.svg';
  metaPixel: string = '../../../../assets/images/meta-pixel.svg';
  selectedIntegrations: any[];
  integrationDuplicateForm: FormGroup;
  integrationDualOwnerForm: FormGroup;
  pixelForm: FormGroup;
  onFilterChanged = onFilterChanged;
  getAppName = getAppName;
  isAccountsLoading: any;
  canBulkAssignment: boolean = false;
  canBulkReassign: boolean = false;
  masterLeadStatus = JSON.parse(localStorage.getItem('masterleadstatus'));
  isCustomStatusEnabled: boolean;
  customStatusList: any[] = [];
  pixelId: any;
  facebookUserId: any;
  isSaveVisible = false;
  initialFormValues: any;
  globalSettings: any;
  showLeftNav: boolean;
  selectedFbAcount: any[];
  fbMarketingData: any[] = []
  fbMarketingIsLoading: boolean = true;
  cplTrackForm: FormGroup;
  userBasicDetails: any;
  currentDate: Date = new Date();
  onPickerOpened = onPickerOpened;
  dateFilterList = DATE_FILTER_LIST;
  cplDateFilterList = DATE_FILTER_LIST?.filter((item: any) => item?.value !== 'TillDate');
  showCplFilter: boolean = false;

  constructor(
    private gridOptionsService: GridOptionsService,
    private gridOptionsExternalService: GridOptionsService,
    private gridOptionsInternalService: GridOptionsService,
    private router: Router,
    private _notificationService: NotificationsService,
    private store: Store<AppState>,
    private headerTitle: HeaderTitleService,
    private route: ActivatedRoute,
    private modalService: BsModalService,
    private modalRef: BsModalRef,
    private _store: Store<AppState>,
    private cdr: ChangeDetectorRef,
    private formBuilder: FormBuilder,
    private shareDataService: ShareDataService,
    private integrationService: IntegrationService,
    private commonService: CommonService
  ) {
    this.headerTitle.setTitle('Facebook');
    if (this.route.snapshot?.routeConfig?.path == 'facebook') {
      this.initialState = {
        displayName: 'Facebook',
        image: 'assets/images/integration/facebook.svg',
        name: 'Facebook',
      };
    }
    this.pixelForm = this.formBuilder.group({
      pixelId: [null, [Validators.required]],
      status: [null, [Validators.required]],
      accessToken: [null, [Validators.required]],
    });
    this.initialFormValues = this.pixelForm.value;
    this.cplTrackForm = this.formBuilder.group({
      cplTrackDType: [null],
      cplTrackRange: ['Today'],
      cplTrackDate: [null],
    });
    this.pixelForm.valueChanges.subscribe(() => {
      this.checkFormChanges();
    });

    this.integrationDuplicateForm = this.formBuilder.group({
      assignedUser: [null, [Validators.required]],
      assignedDuplicateUser: [null, [Validators.required]],
    });

    this.integrationDualOwnerForm = this.formBuilder.group({
      assignedPrimaryUsers: [null, [Validators.required]],
      assignedSecondaryUsers: [null, [Validators.required]],
      assignedDuplicateUser: [null, [Validators.required]],
      selectedUserType: ['Primary User(s)', [Validators.required]],
    });
  }

  ngOnInit() {
    this.store.dispatch(new FetchAgencyNameList());
    this.loadAccounts();
    this.store
      .select(getPriorityList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any[]) => {
        const filteredData = data.filter((item) => item.name === 'SubSource');
        this.moduleId = filteredData.map((item) => item.id).join(', ');
      });
    this._store
      .select(fetchFbAccountFormsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isAccountsLoading = isLoading;
      });

    const selectAndPipe = (selector: any) =>
      this.store.select(selector).pipe(takeUntil(this.stopper));

    selectAndPipe(getPermissions).subscribe((permissions: any) => {
      const permissionsSet = new Set(permissions);
      this.canView = permissionsSet.has('Permissions.Integration.View');
      this.canDelete = permissionsSet.has('Permissions.Integration.Delete');
      this.canAdd = permissionsSet.has('Permissions.Integration.Create');
      this.canAssign = permissionsSet.has('Permissions.Integration.Assign');
      this.canBulkAssignment = permissionsSet.has(
        'Permissions.GlobalSettings.BulkAssignment'
      );
      this.canBulkReassign = permissionsSet.has(
        'Permissions.GlobalSettings.BulkReassign'
      );
      if (permissionsSet.has('Permissions.Users.AssignToAny')) {
        this.canAssignToAny = true;
        this.store.dispatch(new FetchUsersListForReassignment());
      } else {
        this.store.dispatch(new FetchAdminsAndReportees());
      }
      this.initializeGridSettings();
      this.internalFormsGridSettings();
      this.externalFormsGridSettings();
    });

    selectAndPipe(getAdminsAndReportees).subscribe((data: any) => {
      this.userList = data;
      this.activeUsers = data
        ?.filter((user: any) => user.isActive)
        .map((user: any) => ({
          ...user,
          fullName: user.firstName + ' ' + user.lastName,
        }));
      this.activeUsers = assignToSort(this.activeUsers, '');
      this.selectAllForDropdownItems(this.activeUsers);
    });

    selectAndPipe(getUsersListForReassignment).subscribe((data: any) => {
      this.allUserList = data;
      this.allActiveUsers = data
        ?.filter((user: any) => user.isActive)
        .map((user: any) => ({
          ...user,
          fullName: user.firstName + ' ' + user.lastName,
        }));
      this.allActiveUsers = assignToSort(this.allActiveUsers, '');
      this.selectAllForDropdownItems(this.allActiveUsers);
    });

    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userBasicDetails = data;
        this.currentDate = changeCalendar(
          this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
        );
      });
    this.store
      .select(getFacebookMarketing)
      .pipe(takeUntil(this.stopper))
      .subscribe((response: any) => {
        this.fbMarketingData = response;
        this.updateExpandedAccountWithMarketingData();
      });

    this.store
      .select(getFacebookMarketingIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((response: any) => {
        this.fbMarketingIsLoading = response;
        if (this.gridApi) {
          this.gridApi.refreshCells({
            columns: ['Ad Budget', 'Campaign Budget', 'Cost per lead', 'ROI'],
            force: true
          });
        }
      })

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettings = data;
        this.isCustomStatusEnabled = data?.isCustomStatusEnabled;
        this.canEnableAllowDuplicates =
          data?.duplicateFeatureInfo?.isFeatureAdded;
        this.canEnableAllowSecondaryUsers = data?.isDualOwnershipEnabled;
      });

    this._store
      .select(getCustomStatusList)
      .pipe(takeUntil(this.stopper))
      .subscribe((customStatus: any) => {
        this.customStatusList = customStatus
          ?.slice()
          .sort((a: any, b: any) =>
            a?.displayName.localeCompare(b?.displayName)
          );
      });
    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });
    this.store.select(fetchFbAccountForms).subscribe(() => {
      this.closeAllExpandedSections()
    })

    this.cplTrackForm
      .get('cplTrackRange')
      .valueChanges.subscribe((val: any) => {
        if (val == 'Custom') {
          toggleValidation(
            VALIDATION_SET,
            this.cplTrackForm,
            'cplTrackDate',
            [Validators.required]
          );
        } else {
          toggleValidation(
            VALIDATION_CLEAR,
            this.cplTrackForm,
            'cplTrackDate'
          );
        }
      });
  }

  ngAfterViewInit() {
    this.cdr.detectChanges();
  }

  login() {
    this.canAdd
      ? (window.location.href = `https://integration${getEnvDetails()}/facebook?id=${localStorage?.getItem(
        'idToken'
      )}&tenant=${getTenantName()}`)
      : this._notificationService.alert(
        'No Access',
        'You dont have access for this action.'
      );
    return;
  }

  filterTable() {
    const searchText = this.searchText;
    const searchTextLower = searchText.toLowerCase();

    if (searchTextLower.length === 0) {
      this.filteredAccounts = this.allAccounts;
    } else {
      this.filteredAccounts = this.searchAccounts(searchText, this.allAccounts);
    }
  }

  objectIncludesKeyword(obj: any, keyword: string): boolean {
    for (const key in obj) {
      if (
        typeof obj[key] === 'string' &&
        obj[key].toLowerCase().includes(keyword)
      ) {
        return true;
      }
    }
    return false;
  }

  searchAccounts(searchKeyword: string, accounts: any[]): any[] {
    const keyword = searchKeyword.toLowerCase();
    return accounts.map((account: any) => {
      const newAccount: any = {
        ...account,
        allForms: account.allForms?.filter((form: any) => {
          return this.objectIncludesKeyword(form, keyword);
        }),
        ads: account.ads?.filter((ad: any) => {
          return this.objectIncludesKeyword(ad, keyword);
        }),
      };
      return newAccount;
    });
  }

  fetchFbBulkLeads() {
    let initialState: any = {};
    this.modalService.show(
      BulkFetchFBLeads,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
  }

  initDeleteIntegration(id: string, accountName: string) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: accountName,
      fieldType: 'account',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-300 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.deleteFBAccount(id);
        }
      });
    }
  }

  deleteFBAccount(id: string) {
    this.store.dispatch(new DeleteFacebookIntegration(id));
    this.modalRef.hide();
    this.router.navigate(['/global-config']);
  }

  initializeGridSettings() {
    // Ads
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.rowHeight = 60;
    this.gridOptions.columnDefs = [
      {
        headerName: 'Ads',
        field: 'Ads',
        minWidth: 150,
        valueGetter: (params) => params.data?.adName,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Ad Set',
        field: 'Ad Set',
        minWidth: 100,
        valueGetter: (params) => params.data?.adSetName,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Campaign',
        field: 'Campaign',
        width: 100,
        valueGetter: (params) => params.data?.campaignName,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Ad Account',
        field: 'Ad Account',
        minWidth: 100,
        valueGetter: (params) => params.data?.adAccountName,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Ad Budget',
        field: 'Ad Budget',
        minWidth: 120,
        valueGetter: (params) => {
          const budget = params.data?.adBudget;
          return budget !== null && budget !== undefined ? this.globalSettings?.countries?.[0]?.defaultCurrency + ' ' + `${budget}/day` : '--';
        },
        cellRenderer: (params: any) => {
          if (this.fbMarketingIsLoading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
          }
          return `<p class="text-truncate-1">${params?.value}</p>`;
        },
      },
      {
        headerName: 'Campaign Budget',
        field: 'Campaign Budget',
        minWidth: 120,
        valueGetter: (params) => {
          const budget = params.data?.campaignBudget;
          return budget !== null && budget !== undefined ? this.globalSettings?.countries?.[0]?.defaultCurrency + ' ' + `${budget}/day` : '--';
        },
        cellRenderer: (params: any) => {
          if (this.fbMarketingIsLoading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
          }
          return `<p class="text-truncate-1">${params?.value}</p>`;
        },
      },
      {
        headerName: 'Cost per lead',
        field: 'Cost per lead',
        minWidth: 100,
        valueGetter: (params) => {
          const cost = params.data?.costPerLead;
          return cost !== null && cost !== undefined && cost !== 0 ? this.globalSettings?.countries?.[0]?.defaultCurrency + ' ' + `${cost}` : '--';
        },
        cellRenderer: (params: any) => {
          if (this.fbMarketingIsLoading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
          }
          return `<p class="text-truncate-1">${params?.value}</p>`;
        },
      },
      {
        headerName: 'ROI',
        field: 'ROI',
        minWidth: 100,
        valueGetter: (params) => {
          const roi = params.data?.roiPercentage;
          return roi !== null && roi !== undefined && roi !== 0 ? `${roi}%` : '--';
        },
        cellRenderer: (params: any) => {
          if (this.fbMarketingIsLoading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
          }
          return `<p class="text-truncate-1">${params?.value}</p>`;
        },
      },
      {
        headerName: 'Lead Count',
        field: 'Lead Count',
        minWidth: 110,
        valueGetter: (params) => params.data?.leadCount,
        cellRenderer: (params: any) => {
          return `<p>${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Status',
        field: 'Status',
        minWidth: 120,
        valueGetter: (params) => params.data?.status,
        cellRenderer: (params: any) => {
          const status = params.value;
          const classes = this.getStatusClasses(status);
          return `<p class="${classes}">${status}</p>`;
        },
      },
      {
        headerName: 'Agency Name',
        field: 'Agency Name',
        minWidth: 120,
        valueGetter: (params) => params.data?.agencyName,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Actions',
        field: 'Actions',
        width: 150,
        cellRenderer: FacebookActionsComponent,
      },
    ];
    if (this.canBulkReassign || this.canBulkAssignment) {
      this.gridOptions.columnDefs.unshift({
        showRowGroup: true,
        cellRenderer: 'agGroupCellRenderer',
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        checkboxSelection: true,
        maxWidth: 50,
        suppressMovable: true,
      });
    }

    this.gridOptions.context = {
      componentParent: this,
      componentType: 'ads',
    };
  }

  internalFormsGridSettings() {
    // forms
    this.gridOptionsInternal =
      this.gridOptionsInternalService.getGridSettings(this);
    this.gridOptionsInternal.rowHeight = 44;
    this.gridOptionsInternal.columnDefs = [
      {
        headerName: 'Lead Form',
        field: 'Lead Form',
        minWidth: 180,
        valueGetter: (params: any) => params.data?.name,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Form ID',
        field: 'Form ID',
        minWidth: 120,
        valueGetter: (params: any) => params.data?.facebookId,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Page Name',
        field: 'Page Name',
        minWidth: 120,
        valueGetter: (params: any) => params.data?.pageName,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Lead Count',
        field: 'Lead Count',
        minWidth: 100,
        valueGetter: (params: any) => params.data?.leadCount,
        cellRenderer: (params: any) => {
          return `<p>${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Status',
        field: 'Status',
        minWidth: 100,
        valueGetter: (params: any) => params.data?.status,
        cellRenderer: (params: any) => {
          const status = params.value;
          const classes = this.getStatusClasses(status);
          return `<p class="${classes}">${status}</p>`;
        },
      },
      {
        headerName: 'Agency Name',
        field: 'Agency Name',
        minWidth: 100,
        valueGetter: (params: any) => params.data?.agencyName,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Actions',
        field: 'Actions',
        minWidth: 120,
        cellRenderer: FacebookActionsComponent,
      },
    ];

    if (this.canBulkReassign || this.canBulkAssignment) {
      this.gridOptionsInternal.columnDefs.unshift({
        showRowGroup: true,
        cellRenderer: 'agGroupCellRenderer',
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        checkboxSelection: true,
        maxWidth: 50,
        suppressMovable: true,
      });
    }

    this.gridOptionsInternal.context = {
      componentParent: this,
      componentType: 'forms',
    };
  }

  externalFormsGridSettings() {
    this.gridOptionsExternal =
      this.gridOptionsExternalService.getGridSettings(this);
    this.gridOptionsExternal.rowHeight = 44;
    this.gridOptionsExternal.columnDefs = [
      {
        headerName: 'Lead Form',
        field: 'Lead Form',
        minWidth: 180,
        valueGetter: (params: any) => params.data?.name,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Form ID',
        field: 'Form ID',
        minWidth: 120,
        valueGetter: (params: any) => params.data?.facebookId,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Page Name',
        field: 'Page Name',
        minWidth: 120,
        valueGetter: (params: any) => params.data?.pageName,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Lead Count',
        field: 'Lead Count',
        minWidth: 100,
        valueGetter: (params: any) => params.data?.leadCount,
        cellRenderer: (params: any) => {
          return `<p>${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Status',
        field: 'Status',
        minWidth: 100,
        valueGetter: (params: any) => params.data?.status,
        cellRenderer: (params: any) => {
          const status = params.value;
          const classes = this.getStatusClasses(status);
          return `<p class="${classes}">${status}</p>`;
        },
      },
      {
        headerName: 'Agency Name',
        field: 'Agency Name',
        minWidth: 100,
        valueGetter: (params: any) => params.data?.agencyName,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Actions',
        field: 'Actions',
        minWidth: 130,
        cellRenderer: FacebookActionsComponent,
      },
    ];
    if (this.canBulkReassign || this.canBulkAssignment) {
      this.gridOptionsExternal.columnDefs.unshift({
        showRowGroup: true,
        cellRenderer: 'agGroupCellRenderer',
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        checkboxSelection: true,
        maxWidth: 50,
        suppressMovable: true,
      });
    }

    this.gridOptionsExternal.context = {
      componentParent: this,
    };
  }

  isAnyAccountExpanded(): boolean {
    if (!this.filteredAccounts || this.filteredAccounts.length === 0) {
      return false;
    }
    return this.filteredAccounts.some((account: any) =>
      account.isAdsExpanded || account.isFormsExpanded
    );
  }
  mergeMarketingDataWithAds(ads: any[], marketingData: any[]): any[] {
    if (!marketingData || marketingData?.length === 0) {
      return ads;
    }
    const marketingDataMap = new Map();
    marketingData?.forEach(data => {
      marketingDataMap?.set(data?.adId, data);
    });

    return ads.map(ad => {
      const marketingInfo = marketingDataMap?.get(ad?.adId || ad?.id);
      return {
        ...ad,
        adBudget: marketingInfo?.adBudget || null,
        campaignBudget: marketingInfo?.campaignBudget || null,
        costPerLead: marketingInfo?.costPerLead || null,
        roiPercentage: marketingInfo?.roiPercentage || null,
        totalRevenue: marketingInfo?.totalRevenue || null,
      };
    });
  }

  updateExpandedAccountWithMarketingData(): void {
    if (!this.fbMarketingData || this.fbMarketingData?.length === 0) {
      return;
    }
    const expandedAccount = this.filteredAccounts?.find(account => account?.isAdsExpanded);

    if (expandedAccount && expandedAccount?.ads) {
      expandedAccount.ads = this.mergeMarketingDataWithAds(expandedAccount?.ads, this.fbMarketingData);
      if (this.gridApi) {
        this.gridApi?.setRowData(expandedAccount?.ads);
      }
    }
  }

  toggleExpand(clickedAccount: any, key: string): void {
    this.gridApi?.deselectAll();
    // Close all other expanded sections
    this.filteredAccounts = this.filteredAccounts?.map((account: any) => {
      if (account?.id !== clickedAccount?.id) {
        return {
          ...account,
          isAdsExpanded: false,
          isFormsExpanded: false,
        };
      }
      return account;
    });
    clickedAccount[key] = !clickedAccount?.[key];
    if (clickedAccount[key]) {
      if (key === 'isFormsExpanded') {
        clickedAccount.isAdsExpanded = false;
        this.formsPageNumber = 1;
        this.loadFormsForAccount(clickedAccount);
      } else if (key === 'isAdsExpanded') {
        clickedAccount.isFormsExpanded = false;
        this.adsPageNumber = 1;
        // Load ads first, then fetch marketing data in the callback
        this.loadAdsForAccount(clickedAccount);
      }
    } else {
      if (key === 'isFormsExpanded') {
        clickedAccount.isFormsLoading = false;
      } else if (key === 'isAdsExpanded') {
        clickedAccount.isAdsLoading = false;
      }
    }
  }

  fetchMarketingDataForAccount(account: any, ads: any[]): void {
    const accountId = account?.accountId;
    const timeZoneOffset = this.userBasicDetails?.timeZoneInfo?.baseUTcOffset;

    if (!accountId || !ads?.length) {
      return;
    }

    try {
      const campaignAds = ads
        .map((ad: any) => ({
          campaignId: ad?.campaignId,
          adId: ad?.adId || ad?.id
        }))
        .filter(({ campaignId, adId }) => !!campaignId && !!adId);

      if (!campaignAds.length) {
        return;
      }

      const [fromDateRaw, toDateRaw] = getDateRange(DateRange.TillDate, this.currentDate);
      const payload = {
        accountId,
        campaignAds: JSON.stringify(campaignAds),
        DateRange: DateRange.TillDate,
        IsCustomDate: false,
        FromDate: setTimeZoneDate(fromDateRaw || this.currentDate, timeZoneOffset),
        ToDate: setTimeZoneDate(toDateRaw || this.currentDate, timeZoneOffset)
      };

      this.store.dispatch(new FetchFacebookMarketing(payload));
    } catch (error) {
      console.error('fetchMarketingDataForAccount: Error preparing marketing data payload:', error);
    }
  }

  onCplTrackChange() {
    if (!this.cplTrackForm.valid) {
      validateAllFormFields(this.cplTrackForm);
      return;
    }
    const expandedAccount = this.filteredAccounts?.find(account => account?.isAdsExpanded);
    if (expandedAccount && expandedAccount?.ads && expandedAccount?.ads.length > 0) {
      this.fetchMarketingDataWithCustomDateRange(expandedAccount, expandedAccount.ads);
    }
    this.showCplFilter = false;
  }

  fetchMarketingDataWithCustomDateRange(account: any, ads: any[]): void {
    if (!account?.accountId || !ads?.length) {
      return;
    }
    try {
      const timeZoneOffset = this.userBasicDetails?.timeZoneInfo?.baseUTcOffset;
      const selectedDateRange = this.cplTrackForm.value.cplTrackRange !== 'Custom'
        ? DateRange[this.cplTrackForm.value.cplTrackRange as keyof typeof DateRange] || DateRange.TillDate
        : null;

      const dateRangeValues = selectedDateRange !== null
        ? getDateRange(selectedDateRange as DateRange, this.currentDate)
        : null;
      const campaignAds = ads.map((ad: any) => {
        // Try multiple possible property names for campaign ID and ad ID
        const campaignId = ad?.campaignId || ad?.campaign_id || ad?.Campaign_Id || ad?.CampaignId;
        const adId = ad?.adId || ad?.ad_id || ad?.Ad_Id || ad?.AdId || ad?.id || ad?.Id;

        return {
          campaignId: campaignId,
          adId: adId
        };
      }).filter(item => item.campaignId && item.adId);

      if (!campaignAds.length) {
        console.warn('fetchMarketingDataWithCustomDateRange: No valid campaign ads found for CPL marketing data fetch', {
          totalAds: ads.length,
          sampleAdKeys: ads.length > 0 ? Object.keys(ads[0] || {}) : [],
          firstAdSample: ads.length > 0 ? ads[0] : null
        });
        return;
      }
      const cplPayload: any = {
        accountId: account.accountId,
        campaignAds: JSON.stringify(campaignAds),
        DateRange: selectedDateRange,
        IsCustomDate: this.cplTrackForm.value.cplTrackRange === 'Custom',
        FromDate: this.cplTrackForm.value.cplTrackDate && this.cplTrackForm.value.cplTrackRange === 'Custom'
          ? setTimeZoneDate(this.cplTrackForm.value.cplTrackDate[0], timeZoneOffset)
          : setTimeZoneDate(dateRangeValues?.[0], timeZoneOffset),
        ToDate: this.cplTrackForm.value.cplTrackDate && this.cplTrackForm.value.cplTrackRange === 'Custom'
          ? setTimeZoneDate(this.cplTrackForm.value.cplTrackDate[1], timeZoneOffset)
          : setTimeZoneDate(dateRangeValues?.[1] || this.currentDate, timeZoneOffset),
      };
      // Filter out null/undefined values
      const filteredPayload = Object.fromEntries(
        Object.entries(cplPayload).filter(([_, value]) => value !== null && value !== undefined)
      );

      this.store.dispatch(new FetchFacebookMarketing(filteredPayload));
    } catch (error) {
      console.error('Error creating CPL marketing data payload:', error);
    }
  }

  loadAccounts(): void {
    this.isAccountsLoading = true;
    this.integrationService.getAllFbAccounts()
      .pipe(takeUntil(this.stopper))
      .subscribe({
        next: (response: any) => {
          if (response && response.items) {
            this.store.dispatch(new FetchFbAccountFormsSuccess(response.items))
            this.allAccounts = response.items.map((account: any) => ({
              ...account,
              hasAds: true,
              hasForms: true,
              ads: account.ads || [],
              paginatedAds: [],
              paginatedForms: [],
              isAdsLoading: false,
              isFormsLoading: false
            }));
            this.accounts = [...this.allAccounts];
            this.filteredAccounts = [...this.allAccounts];
            this.allAccounts.forEach((account: any) => {
              if (account && account.id) {
                this.checkAccountSubscriptionStatus(account);
              }
            });
          } else {
            this.allAccounts = [];
            this.accounts = [];
            this.filteredAccounts = [];
          }
          this.isAccountsLoading = false;
        },
        error: (error) => {
          this.allAccounts = [];
          this.accounts = [];
          this.filteredAccounts = [];
          this.isAccountsLoading = false;
          console.error('Error loading accounts:', error);
        }
      });
  }

  closeAllExpandedSections(): void {
    if (this.filteredAccounts && this.filteredAccounts.length > 0) {
      this.filteredAccounts = this.filteredAccounts.map((account: any) => ({
        ...account,
        isAdsExpanded: false,
        isFormsExpanded: false,
        isAdsLoading: false,
        isFormsLoading: false
      }));
    }
  }

  getPages(totalCount: number, pageSize: number): number {
    return Math.ceil(totalCount / pageSize);
  }

  loadAdsForAccount(account: any): void {
    this.selectedAccountForAds = account;
    this.isAdsLoading = true;
    account.isAdsLoading = true;

    const searchText = this.searchTerm && this.searchTerm.trim() ? this.searchTerm.trim() : undefined;

    const payload: any = {
      path: 'integration/facebook/account-Ads',
      accountId: account.id,
      PageNumber: this.adsPageNumber,
      PageSize: this.adsPageSize
    };

    if (searchText) {
      payload.SearchText = searchText;
    }

    this.commonService.getModuleListByAdvFilter(payload)
      .pipe(takeUntil(this.stopper))
      .subscribe({
        next: (response: any) => {
          console.log('loadAdsForAccount: Received response:', {
            response: response,
            accountId: account.id,
            accountAccountId: account.accountId,
            isAdsExpanded: account.isAdsExpanded,
            searchTerm: this.searchTerm
          });

          if (response && response.items) {
            account.paginatedAds = response.items;
            this.adsTotalCount = response.totalCount || response.items.length;
            account.hasAds = true;
            account.ads = account.paginatedAds;

            console.log('loadAdsForAccount: Ads loaded successfully', {
              accountId: account.id,
              accountAccountId: account.accountId,
              adsCount: account.ads.length,
              isAdsExpanded: account.isAdsExpanded,
              sampleAd: account.ads.length > 0 ? account.ads[0] : null
            });

            // Fetch marketing data after ads are successfully loaded
            // Always fetch marketing data when ads are loaded, regardless of expanded state
            if (account.ads?.length > 0) {
              console.log('loadAdsForAccount: Calling fetchMarketingDataForAccount');
              this.fetchMarketingDataForAccount(account, account.ads);
            } else {
              console.log('loadAdsForAccount: No ads to fetch marketing data for');
            }
          } else {
            console.log('loadAdsForAccount: No ads data in response');
            account.paginatedAds = [];
            account.ads = [];
            this.adsTotalCount = 0;
            account.hasAds = false;
          }
          this.isAdsLoading = false;
          account.isAdsLoading = false;
        },
        error: (error: any) => {
          account.paginatedAds = [];
          account.ads = [];
          this.adsTotalCount = 0;
          account.hasAds = false;
          this.isAdsLoading = false;
          account.isAdsLoading = false;
          console.error('Error loading ads:', error);
        }
      });
  }

  loadFormsForAccount(account: any): void {
    this.selectedAccountForForms = account;
    this.isFormsLoading = true;
    account.isFormsLoading = true;
    const searchText = this.formsSearchTerm && this.formsSearchTerm.trim() ? this.formsSearchTerm.trim() : undefined;

    const payload: any = {
      path: 'integration/facebook/account-externalforms',
      accountId: account.id,
      PageNumber: this.formsPageNumber,
      PageSize: this.formsPageSize
    };

    if (searchText) {
      payload.SearchText = searchText;
    }

    this.commonService.getModuleListByAdvFilter(payload)
      .pipe(takeUntil(this.stopper))
      .subscribe({
        next: (response: any) => {
          if (response && response.items) {
            account.paginatedForms = response.items;
            this.formsTotalCount = response.totalCount || response.items.length;
            account.hasForms = true;
          } else {
            account.paginatedForms = [];
            this.formsTotalCount = 0;
            account.hasForms = false;
          }
          this.isFormsLoading = false;
          account.isFormsLoading = false;
        },
        error: (error: any) => {
          account.paginatedForms = [];
          this.formsTotalCount = 0;
          account.hasForms = false;
          this.isFormsLoading = false;
          account.isFormsLoading = false;
          console.error('Error loading forms:', error);
        }
      });
  }

  onAdsPageChange(event: number, account: any): void {
    this.adsPageNumber = event + 1;
    this.loadAdsForAccount(account);
  }

  assignAdsCount(account: any): void {
    this.adsPageNumber = 1;
    this.loadAdsForAccount(account);
  }

  onFormsPageChange(event: number, account: any): void {
    this.formsPageNumber = event + 1;
    this.loadFormsForAccount(account);
  }

  assignFormsCount(account: any): void {
    this.formsPageNumber = 1;
    this.loadFormsForAccount(account);
  }

  onSearch(): void {
    console.log('onSearch: Called with searchTerm:', this.searchTerm);
    if (this.searchTerm && this.searchTerm.trim()) {
      this.adsPageNumber = 1;
      if (this.selectedAccountForAds) {
        console.log('onSearch: Loading ads for account:', {
          accountId: this.selectedAccountForAds.id,
          accountAccountId: this.selectedAccountForAds.accountId,
          searchTerm: this.searchTerm
        });
        this.loadAdsForAccount(this.selectedAccountForAds);
      } else {
        console.log('onSearch: No selectedAccountForAds available');
      }
    }
  }

  isEmptyInput(): void {
    console.log('isEmptyInput: Called with searchTerm:', this.searchTerm);
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.adsPageNumber = 1;
      if (this.selectedAccountForAds) {
        console.log('isEmptyInput: Loading ads for account:', {
          accountId: this.selectedAccountForAds.id,
          accountAccountId: this.selectedAccountForAds.accountId
        });
        this.loadAdsForAccount(this.selectedAccountForAds);
      } else {
        console.log('isEmptyInput: No selectedAccountForAds available');
      }
    }
  }

  onFormsSearch(): void {
    if (this.formsSearchTerm && this.formsSearchTerm.trim()) {
      this.formsPageNumber = 1;
      if (this.selectedAccountForForms) {
        this.loadFormsForAccount(this.selectedAccountForForms);
      }
    }
  }

  isEmptyFormsInput(): void {
    if (this.formsSearchTerm === '' || this.formsSearchTerm === null) {
      this.formsPageNumber = 1;
      if (this.selectedAccountForForms) {
        this.loadFormsForAccount(this.selectedAccountForForms);
      }
    }
  }

  openBulkProjectModal(fbAccountName: string, isAdsExpanded: boolean) {
    let initialState: any = {
      gridApi: this.gridApi,
      isForm: !isAdsExpanded,
      fbAccountName,
      closeModal: () => {
        bulkProjectModal?.hide();
      },
    };
    var bulkProjectModal = this.modalService.show(
      FbBulkProjectUpdateComponent,
      {
        class: 'modal-350 right-modal ph-modal-unset',
        keyboard: false,
        initialState,
      }
    );
  }

  openBulkLocationModal(fbAccountName: string, isAdsExpanded: boolean) {
    let initialState: any = {
      gridApi: this.gridApi,
      isForm: !isAdsExpanded,
      fbAccountName,
      closeModal: () => {
        bulkProjectModal?.hide();
      },
    };
    var bulkProjectModal = this.modalService.show(
      FbBulkLocationUpdateComponent,
      {
        class: 'modal-350 right-modal ph-modal-unset',
        keyboard: false,
        initialState,
      }
    );
  }

  openBulkCountryCodeModal(fbAccountName: string, isAdsExpanded: boolean) {
    let initialState: any = {
      gridApi: this.gridApi,
      isForm: !isAdsExpanded,
      fbAccountName,
      closeModal: () => {
        bulkProjectModal?.hide();
      },
    };
    var bulkProjectModal = this.modalService.show(
      FbBulkCountryCodeUpdateComponent,
      {
        class: 'modal-350 right-modal ph-modal-unset',
        keyboard: false,
        initialState,
      }
    );
  }

  openBulkAgencyModal(fbAccountName: string, isAdsExpanded: boolean) {
    let initialState: any = {
      gridApi: this.gridApi,
      isForm: !isAdsExpanded,
      Integartionsource: 'Facebook', // Using string instead of enum
      fbAccountName,
      closeModal: () => {
        bulkBulkAgencyModal?.hide();
      },
    };
    var bulkBulkAgencyModal = this.modalService.show(FbBulkAgencyComponent, {
      class: 'modal-350 right-modal ph-modal-unset',
      keyboard: false,
      initialState,
    });
  }

  openBulkAssignModal(acount: any, isAdsExpanded: boolean) {
    this.store.dispatch(new FetchPriorityList());
    this.selectedFbAcount = this.filteredAccounts?.map((account: any) => account?.facebookAccountName);
    let initialState: any = {
      gridApi: this.gridApi,
      isBulkFb: true,
      isFbComponent: true,
      fbAccountName: isAdsExpanded ? this.selectedFbAcount : acount,
      integration: this.gridApi?.getSelectedNodes(),
      moduleId: this.moduleId,
      isForm: !isAdsExpanded,
      closeModal: () => {
        bulkAssignModal?.hide();
      },
    };
    var bulkAssignModal = this.modalService.show(IntegrationAssignmentV2Component, {
      class: 'modal-700 right-modal ip-modal-unset',
      keyboard: false,
      initialState
    });
  }

  getStatusClasses(status: string): string {
    switch (status) {
      case 'ACTIVE':
        return 'text-accent-green fw-700';
      case 'CAMPAIGN_PAUSED':
      case 'PAUSED':
      case 'ADSET_PAUSED':
        return 'text-dark-yellow';
      case 'WITH_ISSUES':
      case 'DISAPPROVED':
        return 'text-red';
      default:
        return '';
    }
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
  }

  openAssignmentModal(
    _: any,
    integration: any,
    isAd: boolean = false,
    isForms: boolean = false,
  ) {
    this.modalRef = this.modalService.show(IntegrationAssignmentV2Component, {
      class: 'modal-700 right-modal ip-modal-unset',
      keyboard: false,
      initialState: {
        isAdAccount: isAd,
        isFormAccount: isForms,
        integration: integration,
        isFbComponent: true,
      }
    });
    this.selectedAccountId = integration.accountId;
    this.selectedAccountName = integration.accountName;
    this.selectedAdName = integration.adOrFormName;
    this.isAdAccount = integration.isAd;
    this.isFormAccount = integration.isForms;
    this.store.dispatch(new FetchPriorityList());
  }

  toggleAccountSubscription(account: any) {
    this.integrationService.checkFbSubscriptionStatus(account.id)
      .pipe(takeUntil(this.stopper))
      .subscribe({
        next: (statusResponse: any) => {
          if (statusResponse?.succeeded) {
            const currentStatus = statusResponse.data; // true or false
            const isSubscribed = currentStatus; // opposite of current status
            this.integrationService.toggleFbSubscription(account.id, isSubscribed)
              .pipe(takeUntil(this.stopper))
              .subscribe({
                next: (toggleResponse: any) => {
                  if (toggleResponse && toggleResponse.succeeded) {
                    this.loadAccounts();
                    this._notificationService.success(
                      toggleResponse.message || 'Subscription status updated successfully.'
                    );
                  } else {
                    this._notificationService.error(
                      toggleResponse.message || 'Failed to update subscription status.'
                    );
                  }
                },
                error: (error) => {
                  console.error('Error toggling subscription:', error);
                  this._notificationService.error(
                    'Failed to update subscription status.'
                  );
                }
              });
          } else {
            this._notificationService.error(
              'Failed to check current subscription status.'
            );
          }
        },
        error: (error) => {
          console.error('Error checking subscription status:', error);
          this._notificationService.error(
            'Failed to check subscription status.'
          );
        }
      });
  }

  checkAccountSubscriptionStatus(account: any): void {
    if (!account || !account.id) return;
    this.integrationService.checkFbSubscriptionStatus(account.id)
      .pipe(takeUntil(this.stopper))
      .subscribe({
        next: (response: any) => {
          if (response?.succeeded) {
            account.isAllSubscribed = response?.data;
            this.cdr?.detectChanges();
          }
        },
        error: (error: any) => {
          console.error('Error checking subscription status:', error);
        }
      });
  }

  syncAds(accountId: string) {
    this.store.dispatch(new SyncAdsOfAnFBAccount(accountId));
  }

  openCustomFB() {
    const facebookDetails = {
      image: 'assets/images/integration/facebook.svg',
      displayName: 'Facebook',
      name: 'Facebook',
    };
    localStorage.setItem('integrationData', JSON.stringify(facebookDetails));

    this.router.navigate([`/global-config/integration`]);
  }

  selectAllForDropdownItems(items: any[]) {
    let allSelect = (items: any) => {
      items.forEach((element: any) => {
        element['selectedAllGroup'] = 'selectedAllGroup';
      });
    };

    allSelect(items);
  }

  checkFormChanges() {
    this.isSaveVisible =
      this.pixelForm.dirty &&
      JSON.stringify(this.pixelForm.value) !==
      JSON.stringify(this.initialFormValues);
  }

  openPixelModal(pixelModal: TemplateRef<any>, account: any) {
    this.facebookUserId = account?.facebookUserId;
    this.pixelId = account?.pixelId;
    this.modalRef = this.modalService.show(pixelModal, {
      class: 'modal-400 right-modal ip-modal-unset',
      keyboard: false,
    });

    if (account?.pixelId) {
      const selectedStatuses = account.statuses.map((status: any) => ({
        id: status.id,
        displayName: status.displayName,
        selected: false,
      }));

      this.pixelForm.patchValue({
        pixelId: account.pixelId,
        accessToken: account.conversionsAccessToken,
        status: selectedStatuses,
      });
      this.initialFormValues = this.pixelForm.value;
    }
  }

  removeStatus(status: string): void {
    const statusControl = this.pixelForm.get('status');
    if (!statusControl) return;

    const updatedStatus = (statusControl.value || []).filter(
      (item: string) => item !== status
    );
    statusControl.setValue(updatedStatus);
    statusControl.markAsDirty();
    this.pixelForm.markAsDirty();
    this.checkFormChanges();
  }

  onSubmit() {
    if (!this.pixelForm.valid) {
      validateAllFormFields(this.pixelForm);
      return;
    }
    let payload = {
      facebookUserId: this.facebookUserId,
      pixelId: this.pixelForm.value.pixelId,
      conversionAccessToken: this.pixelForm.value.accessToken,
      statusIds: this.pixelForm.value.status?.map((item: any) => item.id),
    };
    this.store.dispatch(new UpdatePixelAccount(payload));
    this.modalRef.hide();
    this.pixelForm.markAsPristine();
    this.initialFormValues = this.pixelForm.value;
    this.isSaveVisible = false;
  }

  openFacebookTracker() {
    this._store.dispatch(new FetchExportFacebookStatus(1, 10));
    this.modalService.show(ExportFacebookTrackerComponent, {
      class: 'modal-1000 modal-dialog-centered h-100 tb-modal-unset',
    });
  }

  closeModal() {
    this.modalService.hide();
    this.assignedUser = [];
    this.isAdAccount = false;
    this.isFormAccount = false;
    this.pixelForm.reset();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
