<!-- Main Integration Content -->
<div class="w-100 h-100 bg-white">
  <!-- Header Section -->
  <div class="flex-between p-16 border-bottom">
    <div class="align-center">
      <img [src]="image" alt="{{displayName}}" class="w-32 h-32 mr-12 br-4">
      <h3 class="fw-semi-bold text-black-100">{{displayName}} Integration</h3>
    </div>
    <div class="align-center">
      <button type="button" class="btn-coal mr-12" (click)="addNewAccount(addAccount)">
        <span class="icon ic-add ic-xs mr-4"></span>
        Add Account
      </button>
    </div>
  </div>

  <!-- Integration List Content -->
  <div class="p-16">
    <!-- Search and Filters -->
    <div class="flex-between mb-16">
      <div class="search-wrapper">
        <input type="text" placeholder="Search accounts..." 
               [(ngModel)]="searchTerm" 
               (keyup)="onSearch($event)"
               (input)="isEmptyInput($event)"
               class="form-control">
      </div>
      <div class="align-center">
        <span class="text-sm text-gray-600 mr-8">Show entries:</span>
        <select [formControl]="pageEntry" class="form-control w-auto">
          <option *ngFor="let size of showEntriesSize" [value]="size">{{size}}</option>
        </select>
      </div>
    </div>

    <!-- Integration Table -->
    <div class="table-responsive">
      <table class="table table-hover">
        <thead>
          <tr>
            <th width="40">
              <input type="checkbox" [checked]="isAllSelected()" 
                     (change)="selectAllRows($event)">
            </th>
            <th>Account Name</th>
            <th>Login Email</th>
            <th>Status</th>
            <th>Created Date</th>
            <th width="120">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let integration of updatedIntegrationList" 
              [class.selected]="integration.isSelected">
            <td>
              <input type="checkbox" [(ngModel)]="integration.isSelected" 
                     (change)="onCheckboxChange($event)">
            </td>
            <td>{{integration.accountName}}</td>
            <td>{{integration.loginEmail}}</td>
            <td>
              <span class="badge" 
                    [class.badge-success]="integration.isActive"
                    [class.badge-secondary]="!integration.isActive">
                {{integration.isActive ? 'Active' : 'Inactive'}}
              </span>
            </td>
            <td>{{integration.createdDate | date:'short'}}</td>
            <td>
              <div class="dropdown">
                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                        data-toggle="dropdown">
                  Actions
                </button>
                <div class="dropdown-menu">
                  <a class="dropdown-item" (click)="openAssignmentModal(integration)">
                    <span class="icon ic-user ic-xs mr-4"></span>
                    Assign
                  </a>
                  <a class="dropdown-item" (click)="initDeleteIntegration(integration.id, integration.accountName)">
                    <span class="icon ic-trash ic-xs mr-4"></span>
                    Delete
                  </a>
                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div class="flex-between mt-16" *ngIf="totalCount > 0">
      <div class="text-sm text-gray-600">
        Showing {{currOffset + 1}} to {{Math.min(currOffset + PageSize, totalCount)}} of {{totalCount}} entries
      </div>
      <nav>
        <ul class="pagination pagination-sm">
          <li class="page-item" [class.disabled]="currPageNumber === 1">
            <a class="page-link" (click)="goToPage(currPageNumber - 1)">Previous</a>
          </li>
          <li class="page-item" *ngFor="let page of getPages(currPageNumber, Math.ceil(totalCount / PageSize))" 
              [class.active]="page === currPageNumber">
            <a class="page-link" (click)="goToPage(page)">{{page}}</a>
          </li>
          <li class="page-item" [class.disabled]="currPageNumber === Math.ceil(totalCount / PageSize)">
            <a class="page-link" (click)="goToPage(currPageNumber + 1)">Next</a>
          </li>
        </ul>
      </nav>
    </div>
  </div>
</div>

<!-- Add Account Modal -->
<ng-template #addAccount>
  <div class="bg-dark w-100 px-16 py-12 text-white flex-between">
    <div class="align-center">
      <img [src]="image" alt="{{displayName}}" class="w-24 h-24 mr-8 br-4">
      <h4 class="fw-semi-bold">Add {{displayName}} Account</h4>
    </div>
    <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="closeModal()"></div>
  </div>
  
  <div class="w-100">
    <div class="py-20">
      <!-- Integration Method Selection (only show if push/pull is enabled) -->
      <div *ngIf="isPushPullEnabled && !selectedIntegrationMethod" class="px-16">
        <h5 class="fw-semi-bold mb-16 text-center">Select Integration Method</h5>
        <div class="d-flex justify-content-center gap-3">
          <div class="border br-8 p-20 text-center cursor-pointer hover-shadow"
               (click)="selectIntegrationMethod('PUSH')"
               style="min-width: 200px;">
            <div class="icon ic-upload ic-large mb-8 text-primary"></div>
            <h6 class="fw-semi-bold mb-8">PUSH API</h6>
            <p class="text-sm text-gray-600">Send data to external system</p>
          </div>
          <div class="border br-8 p-20 text-center cursor-pointer hover-shadow"
               (click)="selectIntegrationMethod('PULL')"
               style="min-width: 200px;">
            <div class="icon ic-download ic-large mb-8 text-primary"></div>
            <h6 class="fw-semi-bold mb-8">PULL API</h6>
            <p class="text-sm text-gray-600">Receive data from external system</p>
          </div>
        </div>
      </div>

      <!-- PUSH Integration Form -->
      <div *ngIf="isPushPullEnabled && selectedIntegrationMethod === 'PUSH'">
        <form [formGroup]="pushForm" (keydown.enter)="$event.preventDefault()">
          <div class="px-16 ip-min-w-350 ip-max-w-350 h-100-130 scrollbar">
            <!-- Account Name -->
            <div>
              <div class="field-label-req">Account Name</div>
              <form-errors-wrapper label="Account Name" [control]="pushForm.controls['accountName']">
                <input type="text" required formControlName="accountName" name="accountName" 
                       autocomplete="off" placeholder="ex. Manasa Pampana" />
              </form-errors-wrapper>
            </div>

            <!-- Login Id/Login Email -->
            <div class="field-label">Login Id/Login Email</div>
            <form-errors-wrapper label="Login Id/Login Email" [control]="pushForm.controls['loginEmail']">
              <input type="email" formControlName="loginEmail" placeholder="ex. <EMAIL>">
            </form-errors-wrapper>

            <!-- Relationship Manager Email -->
            <div class="field-label-req">Relationship Manager Email</div>
            <form-errors-wrapper label="Relationship Manager Email" [control]="pushForm.controls['relationshipManagerEmail']">
              <input type="email" required formControlName="relationshipManagerEmail" 
                     placeholder="ex. <EMAIL>" autocomplete="off" />
            </form-errors-wrapper>

            <!-- Additional Email -->
            <div class="field-label">Additional Email</div>
            <form-errors-wrapper label="Additional Email" [control]="pushForm.controls['additionalEmail']">
              <input type="email" formControlName="additionalEmail" placeholder="ex. <EMAIL>">
            </form-errors-wrapper>

            <!-- BCC Email -->
            <div class="field-label">BCC Email</div>
            <form-errors-wrapper label="BCC Email" [control]="pushForm.controls['bccEmail']">
              <input type="email" formControlName="bccEmail" placeholder="ex. <EMAIL>">
            </form-errors-wrapper>

            <!-- Import cURL -->
            <div class="field-label-req">Import cURL</div>
            <form-errors-wrapper label="Import cURL" [control]="pushForm.controls['curlCommand']">
              <textarea formControlName="curlCommand" rows="4" 
                        placeholder="Paste your cURL command here..." class="w-100"></textarea>
            </form-errors-wrapper>
            <div class="mt-8">
              <button type="button" class="btn-coal btn-sm" (click)="extractFields()" [disabled]="isExtractingFields">
                <span *ngIf="isExtractingFields" class="spinner-border spinner-border-sm mr-2"></span>
                Extract Fields
              </button>
            </div>

            <!-- Payload Mapping -->
            <div class="field-label-req">Payload Mapping</div>
            <div class="border p-12 br-4">
              <div formArrayName="payloadMapping">
                <div *ngFor="let mapping of pushPayloadMappingArray.controls; let i = index" 
                     [formGroupName]="i" class="d-flex align-items-center mb-8">
                  <div class="flex-1 mr-8">
                    <ng-select [items]="availableFields" bindLabel="label" bindValue="value"
                              placeholder="Select Source Field" class="bg-white"
                              formControlName="sourceField" [virtualScroll]="true">
                    </ng-select>
                  </div>
                  <div class="flex-1 mr-8">
                    <input type="text" formControlName="targetField" placeholder="Target Field" />
                  </div>
                  <div class="icon ic-close ic-gray cursor-pointer ml-6 text-red-hover" 
                       (click)="removePayloadMapping(i, 'push')"></div>
                </div>
              </div>
              <button type="button" class="btn-coal btn-sm mt-8" (click)="addPayloadMapping('push')">
                + Add Additional Payload
              </button>
            </div>

            <!-- Extracted Fields Display -->
            <div *ngIf="extractedFields.length > 0" class="mt-16">
              <div class="field-label">Extracted Fields</div>
              <div class="border p-12 br-4 bg-pearl">
                <div class="d-flex flex-wrap">
                  <span *ngFor="let field of extractedFields" 
                        class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                    <span class="fw-600 text-sm text-black-100">{{field}}</span>
                  </span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Action Buttons -->
          <div class="flex-end p-16 box-shadow-20">
            <button type="button" class="btn-gray mr-20" (click)="cancelPushPullIntegration()">Cancel</button>
            <button type="button" class="btn-coal" (click)="savePushPullIntegration()" [disabled]="pushForm.invalid">
              Add Account
            </button>
          </div>
        </form>
      </div>

      <!-- PULL Integration Form -->
      <div *ngIf="isPushPullEnabled && selectedIntegrationMethod === 'PULL'">
        <form [formGroup]="pullForm" (keydown.enter)="$event.preventDefault()">
          <div class="px-16 ip-min-w-350 ip-max-w-350 h-100-130 scrollbar">
            <!-- Webhook URL -->
            <div class="field-label-req">Webhook URL</div>
            <form-errors-wrapper label="Webhook URL" [control]="pullForm.controls['webhookUrl']">
              <div class="flex-between position-relative">
                <input type="url" required formControlName="webhookUrl"
                       placeholder="Enter webhook URL" class="outline-0 padd-r pr-36" autocomplete="off" />
                <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md cursor-pointer"
                     (click)="testWebhook()">
                  <span class="icon ic-connection ic-x-xs"></span>
                </div>
              </div>
            </form-errors-wrapper>

            <!-- Account Name -->
            <div>
              <div class="field-label-req">Account Name</div>
              <form-errors-wrapper label="Account Name" [control]="pullForm.controls['accountName']">
                <input type="text" required formControlName="accountName" name="accountName"
                       autocomplete="off" placeholder="ex. Manasa Pampana" />
              </form-errors-wrapper>
            </div>

            <!-- Login Id/Login Email -->
            <div class="field-label">Login Id/Login Email</div>
            <form-errors-wrapper label="Login Id/Login Email" [control]="pullForm.controls['loginEmail']">
              <input type="email" formControlName="loginEmail" placeholder="ex. <EMAIL>">
            </form-errors-wrapper>

            <!-- Relationship Manager Email -->
            <div class="field-label-req">Relationship Manager Email</div>
            <form-errors-wrapper label="Relationship Manager Email" [control]="pullForm.controls['relationshipManagerEmail']">
              <input type="email" required formControlName="relationshipManagerEmail"
                     placeholder="ex. <EMAIL>" autocomplete="off" />
            </form-errors-wrapper>

            <!-- Additional Email -->
            <div class="field-label">Additional Email</div>
            <form-errors-wrapper label="Additional Email" [control]="pullForm.controls['additionalEmail']">
              <input type="email" formControlName="additionalEmail" placeholder="ex. <EMAIL>">
            </form-errors-wrapper>

            <!-- BCC Email -->
            <div class="field-label">BCC Email</div>
            <form-errors-wrapper label="BCC Email" [control]="pullForm.controls['bccEmail']">
              <input type="email" formControlName="bccEmail" placeholder="ex. <EMAIL>">
            </form-errors-wrapper>

            <!-- Import cURL -->
            <div class="field-label-req">Import cURL</div>
            <form-errors-wrapper label="Import cURL" [control]="pullForm.controls['curlCommand']">
              <textarea formControlName="curlCommand" rows="4"
                        placeholder="Paste your cURL command here..." class="w-100"></textarea>
            </form-errors-wrapper>
            <div class="mt-8">
              <button type="button" class="btn-coal btn-sm" (click)="extractFields()" [disabled]="isExtractingFields">
                <span *ngIf="isExtractingFields" class="spinner-border spinner-border-sm mr-2"></span>
                Extract Fields
              </button>
            </div>

            <!-- Method Type -->
            <div class="field-label-req">Method Type</div>
            <form-errors-wrapper label="Method Type" [control]="pullForm.controls['methodType']">
              <ng-select [items]="methodTypes" bindLabel="label" bindValue="value"
                        placeholder="Select Method Type" class="bg-white"
                        formControlName="methodType" [virtualScroll]="true">
              </ng-select>
            </form-errors-wrapper>

            <!-- Content Type -->
            <div class="field-label-req">Content Type</div>
            <form-errors-wrapper label="Content Type" [control]="pullForm.controls['contentType']">
              <ng-select [items]="contentTypes" bindLabel="label" bindValue="value"
                        placeholder="Select Content Type" class="bg-white"
                        formControlName="contentType" [virtualScroll]="true">
              </ng-select>
            </form-errors-wrapper>

            <!-- Query Parameters -->
            <div class="field-label">Query Parameters</div>
            <div class="border p-12 br-4">
              <div formArrayName="queryParameters">
                <div *ngFor="let param of queryParametersArray.controls; let i = index"
                     [formGroupName]="i" class="d-flex align-items-center mb-8">
                  <div class="flex-1 mr-8">
                    <input type="text" formControlName="key" placeholder="Key" />
                  </div>
                  <div class="flex-1 mr-8">
                    <input type="text" formControlName="value" placeholder="Value" />
                  </div>
                  <div class="icon ic-close ic-gray cursor-pointer ml-6 text-red-hover"
                       (click)="removeQueryParameter(i)"></div>
                </div>
              </div>
              <button type="button" class="btn-coal btn-sm mt-8" (click)="addQueryParameter()">
                + Add Query Parameters
              </button>
            </div>

            <!-- Header Variables -->
            <div class="field-label">Header Variables</div>
            <div class="border p-12 br-4">
              <div formArrayName="headerVariables">
                <div *ngFor="let header of headerVariablesArray.controls; let i = index"
                     [formGroupName]="i" class="d-flex align-items-center mb-8">
                  <div class="flex-1 mr-8">
                    <input type="text" formControlName="key" placeholder="Key" />
                  </div>
                  <div class="flex-1 mr-8">
                    <input type="text" formControlName="value" placeholder="Value" />
                  </div>
                  <div class="icon ic-close ic-gray cursor-pointer ml-6 text-red-hover"
                       (click)="removeHeaderVariable(i)"></div>
                </div>
              </div>
              <button type="button" class="btn-coal btn-sm mt-8" (click)="addHeaderVariable()">
                + Add Header Variables
              </button>
            </div>

            <!-- Body Variables -->
            <div class="field-label">Body Variables</div>
            <div class="border p-12 br-4">
              <div formArrayName="bodyVariables">
                <div *ngFor="let body of bodyVariablesArray.controls; let i = index"
                     [formGroupName]="i" class="d-flex align-items-center mb-8">
                  <div class="flex-1 mr-8">
                    <input type="text" formControlName="key" placeholder="Key" />
                  </div>
                  <div class="flex-1 mr-8">
                    <input type="text" formControlName="value" placeholder="Value" />
                  </div>
                  <div class="icon ic-close ic-gray cursor-pointer ml-6 text-red-hover"
                       (click)="removeBodyVariable(i)"></div>
                </div>
              </div>
              <button type="button" class="btn-coal btn-sm mt-8" (click)="addBodyVariable()">
                + Add Body Variables
              </button>
            </div>

            <!-- Payload Mapping -->
            <div class="field-label-req">Payload Mapping</div>
            <div class="border p-12 br-4">
              <div formArrayName="payloadMapping">
                <div *ngFor="let mapping of pullPayloadMappingArray.controls; let i = index"
                     [formGroupName]="i" class="d-flex align-items-center mb-8">
                  <div class="flex-1 mr-8">
                    <ng-select [items]="availableFields" bindLabel="label" bindValue="value"
                              placeholder="Select Source Field" class="bg-white"
                              formControlName="sourceField" [virtualScroll]="true">
                    </ng-select>
                  </div>
                  <div class="flex-1 mr-8">
                    <input type="text" formControlName="targetField" placeholder="Target Field" />
                  </div>
                  <div class="icon ic-close ic-gray cursor-pointer ml-6 text-red-hover"
                       (click)="removePayloadMapping(i, 'pull')"></div>
                </div>
              </div>
              <button type="button" class="btn-coal btn-sm mt-8" (click)="addPayloadMapping('pull')">
                + Add Additional Payload
              </button>
            </div>

            <!-- Extracted Fields Display -->
            <div *ngIf="extractedFields.length > 0" class="mt-16">
              <div class="field-label">Extracted Fields</div>
              <div class="border p-12 br-4 bg-pearl">
                <div class="d-flex flex-wrap">
                  <span *ngFor="let field of extractedFields"
                        class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                    <span class="fw-600 text-sm text-black-100">{{field}}</span>
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex-end p-16 box-shadow-20">
            <button type="button" class="btn-gray mr-20" (click)="cancelPushPullIntegration()">Cancel</button>
            <button type="button" class="btn-coal" (click)="savePushPullIntegration()" [disabled]="pullForm.invalid">
              Add Account
            </button>
          </div>
        </form>
      </div>

      <!-- Original Integration Form (when push/pull is not enabled) -->
      <div *ngIf="!isPushPullEnabled">
        <!-- Original form content would go here -->
        <!-- This would be the existing integration form -->
      </div>
    </div>
  </div>
</ng-template>
