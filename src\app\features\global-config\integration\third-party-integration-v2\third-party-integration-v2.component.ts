import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  OnD<PERSON>roy,
  OnInit,
  TemplateRef
} from '@angular/core';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { EMPTY_GUID, GOOGLE_BUTTON, PAGE_SIZE, SHOW_ENTRIES } from 'src/app/app.constants';
import { WhatsappThrough } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  getAppName,
  getPages,
  getTenantName,
  isEmptyObject,
  validateAllF<PERSON>Fields
} from 'src/app/core/utils/common.util';
import { ValidationUtil } from 'src/app/core/utils/validation.util';
import {
  FetchIntegrationAssignment,
  FetchPriorityList,
  updateIntegrationAssignment
} from 'src/app/reducers/automation/automation.actions';
import {
  getIntegrationAssignment,
  getPriorityList,
  getUserAssignmentByEntity,
} from 'src/app/reducers/automation/automation.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  AddIntegration,
  AddIntegrationGoogleLead,
  AgencyName,
  AssignPageSize,
  DeleteGoogleIntegration,
  DeleteIntegration,
  DoesExistAccountName,
  FetchAgencyNameList,
  FetchGoogleIntegrationById,
  FetchGoogleIntegrationByIdSuccess,
  FetchGoogleIntegrationList,
  FetchIntegrationById,
  FetchIntegrationByIdSuccess,
  FetchIntegrationList,
  FetchWebhook,
  FetchWebhookAccount,
  UpdateWebhook
} from 'src/app/reducers/Integration/integration.actions';
import {
  doesAccountNameExists,
  getAgencyNameList,
  getExcelFileLink,
  getGoogleIntegrationDetails,
  getGoogleIntegrationIsLoading,
  getIntegrationDetails,
  getIntegrationDetailsIsLoading,
  getWebhook,
  getWebhookAccount,
  getWebhookAccountIsLoading
} from 'src/app/reducers/Integration/integration.reducer';
import { FetchLeadSourceList } from 'src/app/reducers/master-data/master-data.actions';
import { getLeadSource } from 'src/app/reducers/master-data/master-data.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchProjectIdWithName } from 'src/app/reducers/project/project.action';
import { getProjectsIDWithName } from 'src/app/reducers/project/project.reducer';
import { FetchAllLocations } from 'src/app/reducers/site/site.actions';
import { getAllLocations } from 'src/app/reducers/site/site.reducer';
import {
  FetchAdminsAndReportees,
  FetchUsersListForReassignment,
  FetchWhatsappSettingList,
  UpdateWhatsappSettingList,
} from 'src/app/reducers/teams/teams.actions';
import {
  getAdminsAndReportees,
  getUsersListForReassignment,
  getWhatsappSetting,
} from 'src/app/reducers/teams/teams.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { environment as env } from 'src/environments/environment';
import { IntegrationAssignmentV2Component } from '../integration-assignment-v2/integration-assignment-v2.component';
import { SourceService } from 'src/app/services/controllers/source.service';
import { AddSourceAccount } from 'src/app/reducers/source/source.actions';

interface GoogleOAuth {
  oAuthEndpoint: string;
  clientId: string;
  scope: string;
  googleButton: string;
}

@Component({
  selector: 'third-party-integration-v2',
  templateUrl: './third-party-integration-v2.component.html',
})
export class ThirdPartyIntegrationV2Component
  implements OnInit, OnDestroy, AfterViewInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  public searchTermSubject = new Subject<string>();
  
  // Existing properties from original component
  settingForm: FormGroup;
  assignedUser: any[] = [];
  assignedDuplicateUser: any[] = [];
  project: FormControl = new FormControl(null);
  location: FormControl = new FormControl(null);
  countryCode: FormControl = new FormControl(null);
  agencyName: FormControl = new FormControl(null);
  name: string = '';
  domainName: URL = new URL(location.href);
  googleButton = GOOGLE_BUTTON;
  updatedIntegrationList: Array<any> = [];
  assignedUserDetails: Array<string> = [];
  userList: Array<any> = [];
  allUserList: Array<any> = [];
  activeUsers: Array<any> = [];
  allActiveUsers: Array<any> = [];
  allProjectList: Array<any> = [];
  placesList: Array<any> = [];
  bulkModuleId: string;
  searchTerm: string;
  selectedAccountName: string;
  selectedIntegrations: Array<any> = [];
  selectedAccountId: string = '';
  selectedLeadSource: string;
  agencyAccountId: any;
  agencySource: string;
  agencyNameList: string;
  canAdd: boolean = false;
  canDelete: boolean = false;
  canAssign: boolean = false;
  canAssignToAny: boolean = false;
  isShowContentAndAdd: boolean = true;
  isShowAddAccountBtn: boolean = true;
  isShowAddAtListing: boolean = true;
  isShowAssignModal: boolean = false;
  isShowProjectAndLocationModal: boolean = false;
  isShowAgencyModal: boolean = false;
  selectedCount: number = 0;
  isBulkAssignModel: boolean = false;
  displayName: string;
  isManualChange: boolean = true;
  lastClickedOption: any;
  cities: any = [];
  canAllowDuplicates: boolean = false;
  canEnableAllowDuplicates: boolean = false;
  canAllowSecondaryUsers: boolean = false;
  message: string = '';
  notes: string = '';
  canEnableAllowSecondaryUsers: boolean = false;
  assignedSecondaryUsers: any[] = [];
  assignedPrimaryUsers: any[] = [];
  sameAsPrimaryUsers: boolean = false;
  sameAsSelectedUsers: boolean = false;
  sameAsAbove: boolean = false;
  integrationDuplicateForm: FormGroup;
  integrationDualOwnerForm: FormGroup;
  isIntegrationLoading: boolean = true;
  isShowCountryCodeModal: boolean = false;
  preferredCountries: any[] = [];
  integrationAssignmentData: any;
  webhookfields: any;
  webHookList: any;
  webhookKeys: string[];
  payloadfieldkey: any;
  webhookMappingForm: FormGroup;
  integrationForm: FormGroup;
  payloadOptions: any;
  tempVariables: any;
  uniqueValueArray: any[] = [];
  tenantId: string = getTenantName();
  temp: any[];
  allPayloadOptions: any[];
  leadSources: any[] = [];
  selectedInfo: any;
  isEditWebhook: boolean = false;
  iswebhookAccountLoading: boolean = true;
  doesAccountNameExist: boolean = false;
  canBulkAssignment: boolean = false;
  canBulkReassign: boolean = false;
  getAppName = getAppName;
  image: string;
  receivedData: any;
  email: any
  isSearch: boolean = false;
  isConfigVisible: boolean = false;
  personalList: any[];
  integrationList: any[];
  settingData: any;
  personalList1: any[];
  integrationList1: any[];
  canViewForFilter: boolean;
  currOffset: number = 0;
  currPageNumber: number = 1;
  PageSize: number = PAGE_SIZE;
  totalCount: number;
  getPages = getPages;
  showEntriesSize: number[] = SHOW_ENTRIES;
  pageEntry: FormControl = new FormControl(this.PageSize);
  rowData: any[];
  filtersPayload: any;

  // New properties for push/pull functionality
  isPushPullEnabled: boolean = false; // This will come from backend
  pushForm: FormGroup;
  pullForm: FormGroup;
  extractedFields: string[] = [];
  defaultPayloadMappings: any[] = [];
  availableFields: any[] = [];
  isExtractingFields: boolean = false;
  selectedIntegrationMethod: string = '';

  methodTypes = [
    { value: 'GET', label: 'GET' },
    { value: 'POST', label: 'POST' }
  ];

  contentTypes = [
    { value: 'form-data', label: 'form-data' },
    { value: 'x-www-form-urlencoded', label: 'x-www-form-urlencoded' },
    { value: 'application/json', label: 'application/json' }
  ];

  constructor(
    public modalService: BsModalService,
    private modalRef: BsModalRef,
    private _store: Store<AppState>,
    public router: Router,
    private cdr: ChangeDetectorRef,
    private formBuilder: FormBuilder,
    private _notificationsService: NotificationsService,
    private fb: FormBuilder,
    private _translateService: TranslateService,
    private headerTitle: HeaderTitleService,
    private sourceService: SourceService
  ) {
    this.headerTitle.setLangTitle('Integration');
    this.integrationDuplicateForm = this.formBuilder.group({
      assignedUser: [null, [Validators.required]],
      assignedDuplicateUser: [null, [Validators.required]],
    });

    const storedData = localStorage.getItem('integrationData');
    if (storedData) {
      this.receivedData = JSON.parse(storedData);
      this.image = this.receivedData.image;
      this.displayName = this.receivedData.displayName;
      this.name = this.receivedData.name;
      // Check if push/pull is enabled for this source
      this.isPushPullEnabled = this.receivedData.isPushPullEnabled || false;
    }

    this.initializeForms();
    this.setupStoreSubscriptions();
  }

  private initializeForms(): void {
    // Original integration form
    this.integrationForm = this.fb.group({
      accountName: ['', [Validators.required, ValidationUtil.cannotBeBlank]],
      loginEmail: [null, ValidationUtil.emailValidatorMinLength],
      toRecipients: this.fb.array([], [Validators.required, Validators.minLength(1)]),
      ccRecipients: this.fb.array([]),
      bccRecipients: this.fb.array([])
    });

    // Push integration form
    this.pushForm = this.fb.group({
      accountName: ['', Validators.required],
      loginId: [''],
      loginEmail: [''],
      relationshipManagerEmail: ['', [Validators.required, Validators.email]],
      additionalEmail: ['', Validators.email],
      bccEmail: ['', Validators.email],
      curlCommand: ['', Validators.required],
      payloadMapping: this.fb.array([])
    });

    // Pull integration form
    this.pullForm = this.fb.group({
      webhookUrl: ['', Validators.required],
      accountName: ['', Validators.required],
      loginId: [''],
      loginEmail: [''],
      relationshipManagerEmail: ['', [Validators.required, Validators.email]],
      additionalEmail: ['', Validators.email],
      bccEmail: ['', Validators.email],
      curlCommand: ['', Validators.required],
      methodType: ['GET', Validators.required],
      contentType: ['application/json', Validators.required],
      payloadMapping: this.fb.array([]),
      queryParameters: this.fb.array([]),
      headerVariables: this.fb.array([]),
      bodyVariables: this.fb.array([])
    });

    this.integrationDualOwnerForm = this.formBuilder.group({
      assignedPrimaryUsers: [null, [Validators.required]],
      assignedSecondaryUsers: [null, [Validators.required]],
      assignedDuplicateUser: [null, [Validators.required]],
      selectedUserType: ['Primary User(s)', [Validators.required]],
    });

    // Add default payload mappings for push/pull forms
    this.addDefaultPayloadMappings();
  }

  private setupStoreSubscriptions(): void {
    // Copy all the store subscriptions from the original component
    // This is a simplified version - you would copy all the subscriptions from the constructor
    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Integration.Delete'))
          this.canDelete = true;
        if (permissions?.includes('Permissions.Integration.Create'))
          this.canAdd = true;
        if (permissions?.includes('Permissions.Integration.Assign'))
          this.canAssign = true;
        if (permissions?.includes('Permissions.Users.ViewForFilter')) {
          this.canViewForFilter = true;
          if (permissions?.includes('Permissions.Users.AssignToAny')) {
            this.canAssignToAny = true;
            this._store.dispatch(new FetchUsersListForReassignment());
          } else {
            this._store.dispatch(new FetchAdminsAndReportees());
          }
        }
      });

    // Add other necessary subscriptions...
  }

  ngOnInit() {
    this._store.dispatch(
      new AssignPageSize(this.PageSize, this.currPageNumber)
    );
    this.settingForm = this.fb.group({
      personal: [null],
      integration: [null],
    });

    this.loadDefaultMappings();
    this.sourceAndSearch();
  }

  ngAfterViewInit() {
    this.cdr.detectChanges();
  }

  ngOnDestroy() {
    this.stopper.emit();
    this.stopper.complete();
  }

  // Push/Pull specific methods
  get pushPayloadMappingArray(): FormArray {
    return this.pushForm.get('payloadMapping') as FormArray;
  }

  get pullPayloadMappingArray(): FormArray {
    return this.pullForm.get('payloadMapping') as FormArray;
  }

  get queryParametersArray(): FormArray {
    return this.pullForm.get('queryParameters') as FormArray;
  }

  get headerVariablesArray(): FormArray {
    return this.pullForm.get('headerVariables') as FormArray;
  }

  get bodyVariablesArray(): FormArray {
    return this.pullForm.get('bodyVariables') as FormArray;
  }

  createPayloadMappingGroup(sourceField: string = '', targetField: string = ''): FormGroup {
    return this.fb.group({
      sourceField: [sourceField, Validators.required],
      targetField: [targetField, Validators.required]
    });
  }

  createKeyValueGroup(key: string = '', value: string = ''): FormGroup {
    return this.fb.group({
      key: [key, Validators.required],
      value: [value, Validators.required]
    });
  }

  addDefaultPayloadMappings(): void {
    // Pre-selected default mappings as per requirements
    const defaultMappings = [
      { sourceField: 'lead_name', targetField: 'Lead Name' },
      { sourceField: 'primary_no', targetField: 'Primary No' },
      { sourceField: 'secondary_no', targetField: 'Secondary No' }
    ];

    // Add to push form
    defaultMappings.forEach(mapping => {
      this.pushPayloadMappingArray.push(
        this.createPayloadMappingGroup(mapping.sourceField, mapping.targetField)
      );
    });

    // Add to pull form
    defaultMappings.forEach(mapping => {
      this.pullPayloadMappingArray.push(
        this.createPayloadMappingGroup(mapping.sourceField, mapping.targetField)
      );
    });
  }

  // Method selection
  selectIntegrationMethod(method: string): void {
    this.selectedIntegrationMethod = method;
  }

  // Extract fields from cURL
  extractFields(): void {
    const form = this.selectedIntegrationMethod === 'PUSH' ? this.pushForm : this.pullForm;
    const curlCommand = form.get('curlCommand')?.value;
    
    if (!curlCommand) {
      this._notificationsService.error('Please enter a cURL command first.');
      return;
    }

    this.isExtractingFields = true;
    this.sourceService.extractFieldsFromCurl(curlCommand).subscribe({
      next: (response) => {
        if (response && response.fields) {
          this.extractedFields = response.fields;
          this._notificationsService.success('Fields extracted successfully.');
          this.updateAvailableFields();
        }
        this.isExtractingFields = false;
      },
      error: (error) => {
        this._notificationsService.error('Failed to extract fields from cURL command.');
        this.isExtractingFields = false;
      }
    });
  }

  updateAvailableFields(): void {
    this.availableFields = this.extractedFields.map(field => ({
      value: field,
      label: field
    }));
  }

  loadDefaultMappings(): void {
    this.sourceService.getDefaultPayloadMappings().subscribe({
      next: (response) => {
        if (response && response.succeeded) {
          this.defaultPayloadMappings = response.data;
        }
      },
      error: (error) => {
        console.error('Failed to load default mappings:', error);
      }
    });
  }

  // Test webhook connection (for pull integration)
  testWebhook(): void {
    const webhookUrl = this.pullForm.get('webhookUrl')?.value;
    if (!webhookUrl) {
      this._notificationsService.error('Please enter webhook URL first.');
      return;
    }

    const testPayload = {
      test: true,
      timestamp: new Date().toISOString()
    };

    this.sourceService.testWebhookConnection(webhookUrl, testPayload).subscribe({
      next: (response) => {
        if (response && response.succeeded) {
          this._notificationsService.success('Webhook connection test successful.');
        } else {
          this._notificationsService.error('Webhook connection test failed.');
        }
      },
      error: (error) => {
        this._notificationsService.error('Webhook connection test failed.');
      }
    });
  }

  // Add/Remove payload mappings
  addPayloadMapping(formType: 'push' | 'pull'): void {
    const array = formType === 'push' ? this.pushPayloadMappingArray : this.pullPayloadMappingArray;
    array.push(this.createPayloadMappingGroup());
  }

  removePayloadMapping(index: number, formType: 'push' | 'pull'): void {
    const array = formType === 'push' ? this.pushPayloadMappingArray : this.pullPayloadMappingArray;
    array.removeAt(index);
  }

  // Add/Remove query parameters (pull only)
  addQueryParameter(): void {
    this.queryParametersArray.push(this.createKeyValueGroup());
  }

  removeQueryParameter(index: number): void {
    this.queryParametersArray.removeAt(index);
  }

  // Add/Remove header variables (pull only)
  addHeaderVariable(): void {
    this.headerVariablesArray.push(this.createKeyValueGroup());
  }

  removeHeaderVariable(index: number): void {
    this.headerVariablesArray.removeAt(index);
  }

  // Add/Remove body variables (pull only)
  addBodyVariable(): void {
    this.bodyVariablesArray.push(this.createKeyValueGroup());
  }

  removeBodyVariable(index: number): void {
    this.bodyVariablesArray.removeAt(index);
  }

  // Save push/pull integration
  savePushPullIntegration(): void {
    const form = this.selectedIntegrationMethod === 'PUSH' ? this.pushForm : this.pullForm;
    
    if (form.invalid) {
      this._notificationsService.error('Please fill all required fields.');
      return;
    }

    const formData = {
      sourceId: this.receivedData?.id,
      integrationMethod: this.selectedIntegrationMethod,
      ...form.value
    };

    this._store.dispatch(new AddSourceAccount(formData));
    this.modalService.hide();
  }

  // Cancel push/pull integration
  cancelPushPullIntegration(): void {
    this.selectedIntegrationMethod = '';
    this.modalService.hide();
  }

  // Copy existing methods from original component
  sourceAndSearch() {
    this.PageSize = this.pageEntry.value;
    this.currOffset = 0;
    this.currPageNumber = 1;
    if (this.displayName == 'Google ads leads form') {
      const payload = {
        LeadSource: this.name,
        SearchByName: this.searchTerm,
        PageSize: this.PageSize,
        PageNumber: this.currPageNumber,
      };
      this._store.dispatch(new FetchGoogleIntegrationList(payload));
    } else {
      const payload = {
        LeadSource: this.name,
        SearchByName: this.searchTerm,
        PageSize: this.PageSize,
        PageNumber: this.currPageNumber,
      };
      this._store.dispatch(new FetchIntegrationList(payload));
    }
    if (
      this.displayName == 'Google ads landing page' ||
      this.displayName == 'Google ads leads form' ||
      this.displayName == 'Microsoft Ads'
    ) {
      this._store.dispatch(new FetchAgencyNameList());
    }
  }

  // Modal methods
  addNewAccount(addAccount: TemplateRef<any>) {
    this.isEditWebhook = false;
    this.webhookMappingForm?.reset();
    this.integrationForm.reset();
    this.pushForm.reset();
    this.pullForm.reset();
    this.selectedIntegrationMethod = '';
    this.addDefaultParameters();
    this.addDefaultPayloadMappings();
    this.deselectAllRowsAndAdd();
    this.resetIntegrationRecipients();
    this.resetWebhookRecipients();
    let initialState: any = {
      class: 'modal-550 right-modal ip-modal-unset',
    };
    this.modalRef = this.modalService.show(
      addAccount,
      initialState
    );
  }

  closeModal() {
    this.modalService.hide();
    this.reset();
  }

  reset() {
    this.updatedIntegrationList?.forEach((item) => (item.isSelected = false));
    this.selectedCount = 0;
    this.integrationForm.reset();
    this.pushForm.reset();
    this.pullForm.reset();
    this.selectedIntegrationMethod = '';
    this.assignedUser = [];
    this.project.reset();
    this.location.reset();
    this.agencyName.reset();
    if (this.displayName == 'Webhook') {
      this.isEditWebhook = false;
      this.webhookMappingForm?.reset();
      this.addDefaultParameters();
    }
  }

  // Helper methods
  deselectAllRowsAndAdd() {
    this.selectAllRows({ target: { checked: false } });
  }

  selectAllRows(event: any) {
    this.updatedIntegrationList?.forEach((integration: any) => {
      integration.isSelected = event.target.checked;
    });
    this.onCheckboxChange(event);
    this.selectedCount = event.target.checked
      ? this.updatedIntegrationList?.length
      : 0;
  }

  onCheckboxChange(event: any) {
    const isChecked = event.target.checked;
    this.isBulkAssignModel = true;
    if (isChecked) {
      this.selectedCount++;
    } else {
      this.selectedCount--;
    }
  }

  resetIntegrationRecipients() {
    // Implementation for resetting integration recipients
  }

  resetWebhookRecipients() {
    // Implementation for resetting webhook recipients
  }

  addDefaultParameters() {
    // Implementation for adding default parameters
  }

  // Validation methods
  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}
