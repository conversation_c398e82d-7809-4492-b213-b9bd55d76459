<div class="br-4">
  <h5 class="text-white fw-600 bg-black px-20 py-12">
    {{ (editMode ? 'Edit' : 'Add New') }} Source & Sub-Source
  </h5>
  <form [formGroup]="sourceForm" autocomplete="off" class="pb-20 px-30">
    <div class="label-req">Source Name</div>
    <form-errors-wrapper [control]="sourceForm.controls['sourceName']" label="source name">
      <input type="text" appDebounceInput (debounceEvent)="doesSourceExist($event)" class="form-control"
        placeholder="Type here..." formControlName="sourceName">
    </form-errors-wrapper>
    <div class="">
      <div class="label">Add Sub-Source name</div>
      <form-errors-wrapper [control]="sourceForm.controls['subSourceName']" label="sub-source name">
        <div class="d-flex">
          <input type="text" class="form-control" placeholder="Type here..." formControlName="subSourceName">
          <div class="bg-black align-center justify-center w-35px h-35px br-50 ml-10 cusrsor-pointer"
            (click)="addSubSource()" [ngClass]="{'pe-none': !sourceForm.get('subSourceName').value}">
            <span class="icon ic-add "></span>
          </div>
        </div>
      </form-errors-wrapper>
    </div>
    <div class="" *ngIf="subSources.length > 0">
      <div class="d-flex flex-wrap">
        <div *ngFor="let subSource of subSources; let i = index"
          class="mr-10 mb-10 bg-light-pearl px-10 py-5 br-4 d-flex align-items-center">
          {{ subSource }}
          <span class="icon ic-x-circle ic-xxs ic-slate-90 cursor-pointer ml-5" (click)="removeSubSource(i)"></span>
        </div>
      </div>
    </div>
    <div class="">
      <div class="label">Source Logo</div>
      <div class="d-flex w-100 align-items-center">
        <div class="border w-100 br-4 p-10 d-flex align-items-center h-40" *ngIf="logoPreview">
          <div class="mr-10 d-flex align-items-center">
            <span class="icon ic-paper-clip ic-xs ic-black mr-8"></span>
            <span>{{ logoFileName | slice:0:20 }}</span>
          </div>
          <div class="d-flex ml-auto">
            <span class="text-primary cursor-pointer mr-10 text-decoration-underline"
              (click)="logoFileInput.click()">Replace</span>
            <span class="text-danger cursor-pointer text-decoration-underline" (click)="removeLogo()">Delete</span>
          </div>
        </div>
        <div *ngIf="!logoPreview" class="w-100 border br-4 p-10 cusrsor-pointer" (click)="logoFileInput.click()">
          <div class="align-center gap-2">
            <span class="icon ic-paper-clip ic-xs ic-black mr-8"></span>
            <span class="text-gray cursor-pointer">Select an image</span>
          </div>
        </div>
        <input type="file" #logoFileInput style="display: none;" (change)="onLogoSelected($event)" accept="image/*">
      </div>
      <!-- <small class="text-muted">If no logo is selected, a default logo will be applied automatically.</small> -->
    </div>
    <div class="mt-10">
      <div class="label">Enabled For</div>
      <div class="align-center flex-nowrap">
        <ng-container *ngFor="let type of ['Lead','Data','Both',]">
          <label class="form-check form-check-inline bg-light-pearl br-20 p-10 px-16" for="inpOtpReceiver{{type}}"
            style="margin-right: 15px;">
            <input type="radio" id="inpOtpReceiver{{type}}" name="receiveReports" formControlName="enabledFor"
              [value]="type" class="radio-check-input">
            <div [ngClass]="sourceForm.get('enabledFor').value === type ? 'text-dark':'text-dark-gray'"
              class="cursor-pointer text-large text-sm ml-6">
              {{type}}</div>
          </label>
        </ng-container>
      </div>
    </div>
    <div class="d-flex gap-2 mt-16 justify-end bg-white position-sticky bottom-0">
      <button type="button" class="btn-gray" (click)="cancel()">Cancel</button>
      <button type="submit" class="btn-coal" (click)="save()" [disabled]="sourceForm.invalid">
        {{ editMode ? 'Update' : 'Add' }}
      </button>
    </div>
  </form>
</div>