import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { NotificationsService } from 'angular2-notifications';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { AddSource, ExistSource, UpdateSource } from 'src/app/reducers/source/source.actions';
import { getSourceExist } from 'src/app/reducers/source/source.reducer';

@Component({
  selector: 'add-source',
  templateUrl: './add-source.component.html'
})
export class AddSourceComponent implements OnInit {

  sourceForm: FormGroup;
  subSources: string[] = [];
  logoFile: File | null = null;
  logoPreview: string | null = null;
  logoFileName: string | null = null;
  editMode: boolean = false;
  sourceData: any = null;
  doesExistSourceName: boolean;

  constructor(
    private fb: FormBuilder,
    public modalRef: BsModalRef,
    private notificationService: NotificationsService,
    private store: Store<AppState>
  ) { }

  ngOnInit(): void {
    this.initForm();

    if (this.sourceData) {
      this.editMode = true;
      this.patchFormValues();
    }
  }

  initForm(): void {
    this.sourceForm = this.fb.group({
      sourceName: [null, Validators.required],
      subSourceName: [null],
      enabledFor: ['Lead', Validators.required]
    });
  }

  doesSourceExist(sourceName: string) {
    if (sourceName) {
      this.store.dispatch(new ExistSource(sourceName));
      this.store.select(getSourceExist).subscribe((doesExistSourceName: boolean) => {
        this.doesExistSourceName = doesExistSourceName;
        this.sourceForm.get('sourceName').setErrors(
          doesExistSourceName ? { alreadyExist: true } : null
        );
      });
    }
  }

  patchFormValues(): void {
    if (this.sourceData) {
      this.sourceForm.patchValue({
        sourceName: this.sourceData.displayName,
        enabledFor: this.sourceData.enabledFor || 'Lead'
      });

      if (this.sourceData.subSources && this.sourceData.subSources.length > 0) {
        this.subSources = [...this.sourceData.subSources];
      }

      if (this.sourceData.imageURL) {
        this.logoPreview = this.sourceData.imageURL;
        this.logoFileName = this.sourceData.imageName || 'existing-logo.png';
      }
    }
  }

  addSubSource(): void {
    const subSourceName = this.sourceForm.get('subSourceName').value;
    if (subSourceName && !this.subSources.includes(subSourceName)) {
      this.subSources.push(subSourceName);
      this.sourceForm.get('subSourceName').setValue('');
    } else if (this.subSources.includes(subSourceName)) {
      this.notificationService.error('This sub-source already exists.');
    }
  }

  removeSubSource(index: number): void {
    this.subSources.splice(index, 1);
  }

  onLogoSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.logoFile = file;
      this.logoFileName = file.name;
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.logoPreview = e.target.result;
      };
      reader.readAsDataURL(file);
    }
  }

  removeLogo(): void {
    this.logoFile = null;
    this.logoPreview = null;
    this.logoFileName = null;
  }

  save(): void {
    if (this.sourceForm.invalid) {
      console.log('Form is invalid:', this.sourceForm.errors);
      return;
    }
    const subSourcesArray = this.subSources.length > 0 ? [...this.subSources] : [];
    const formData = {
      sourceName: this.sourceForm.get('sourceName').value,
      subSources: subSourcesArray,
      enabledFor: this.sourceForm.get('enabledFor').value,
      logoFile: this.logoFile
    };

    console.log('Submitting form data:', formData);

    try {
      if (this.editMode) {
        console.log('Dispatching UpdateSource action');
        this.store.dispatch(new UpdateSource({
          ...formData,
          id: this.sourceData.id
        }));
        this.notificationService.success('Processing source update...');
      } else {
        console.log('Dispatching AddSource action');
        this.store.dispatch(new AddSource(formData));
        this.notificationService.success('Processing source addition...');
      }

      // Close the modal
      this.modalRef.hide();
    } catch (error) {
      console.error('Error dispatching action:', error);
      this.notificationService.error('An error occurred. Please try again.');
    }
  }
  cancel(): void {
    this.modalRef.hide();
  }
}
