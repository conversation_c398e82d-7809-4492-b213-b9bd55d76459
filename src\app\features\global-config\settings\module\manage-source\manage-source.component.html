<div class="pt-20 px-30">
  <div class="bg-light-pearl">
    <div class="flex-between">
      <div class="align-center">
        <div class="icon ic-chevron-left ic-xxs ic-coal cursor-pointer mr-12" (click)="goBack()"></div>
        <span class="icon ic-circle-nodes ic-sm ic-blue-1150 mr-8"></span>
        <h5 class="fw-600 header-3">Manage Source</h5>
      </div>
      <div class="d-flex align-items-center btn-coal" (click)="openAddSourceModal()">
        <span class="ic-add icon ic-xxs"></span>
        <span class="ml-8 ip-d-none">Add New Source</span>
      </div>
    </div>
    <div class="pt-16">
      <div class="bg-white w-100 border-gray">
        <form autocomplete="off" class="align-center py-10 px-12 no-validation">
          <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"> </span>
          <input placeholder="type to search" class="border-0 outline-0 w-100" autocomplete="off"
            [(ngModel)]="searchTerm" name="searchTerm" id="inpSearchCustomFlag"
            (ngModelChange)="updateFilteredSources()">
          <span *ngIf="searchTerm" class="icon ic-x-circle ic-sm ic-slate-90 cursor-pointer"
            (click)="clearSearch()"></span>
        </form>
      </div>
      <div class="px-16 py-12 bg-white">
        <div *ngIf="!sourcesLoading; else loadingTemplate">
          <div class="h-100-240 pinned-grid position-relative">
            <!-- No sources found message -->
            <div *ngIf="filteredSources.length === 0 && !sourcesLoading" class="flex-col flex-center h-100-270">
              <img src="assets/images/layered-cards.svg" alt="No data found" width="160" height="140">
              <div class="fw-semi-bold text-xl text-mud">No sources found!</div>
              <p *ngIf="searchTerm" class="text-center mt-2">
                No sources match the search term "{{searchTerm}}". <a href="javascript:void(0)" (click)="clearSearch()"
                  class="text-accent-green">Clear search</a>
              </p>
            </div>
            <ag-grid-angular *ngIf="filteredSources.length > 0" #agGrid class="ag-theme-alpine w-100 h-100"
              [gridOptions]="gridOptions" [rowData]="filteredSources" [suppressPaginationPanel]="true"
              [alwaysShowHorizontalScroll]="true" [alwaysShowVerticalScroll]="true" (gridReady)="onGridReady($event)"
              (cellClicked)="onCellClicked($event)" (selectionChanged)="onSelectionChanged($event)">
            </ag-grid-angular>
          </div>
          <div class="flex-between ip-col-reverse ip-flex-end p-16 ip-px-4" *ngIf="filteredSources?.length">
            <div class="mr-10 ip-mt-10">
              Showing {{(currOffset * pageSize) + 1}} to
              {{((currOffset * pageSize) + pageSize > totalCount ? totalCount : (currOffset * pageSize) + pageSize)}} of
              {{totalCount}} entries
            </div>
            <div class="show-dropdown-white flex-center ph-flex-col ph-flex-end">
              <div class="flex-center">
                Entries per page
                <ng-select [virtualScroll]="true" [placeholder]="pageSize" bindValue="id" [searchable]="false"
                  ResizableDropdown class="w-80" (change)="onPageSizeChange()" [(ngModel)]="selectedPageSize">
                  <ng-option name="showEntriesSize" *ngFor="let size of pageSizeOptions" [value]="size">
                    {{size}}
                  </ng-option>
                </ng-select>
              </div>
              <div class="mx-8 my-4 border-right h-16 ph-d-none"></div>
              <pagination [offset]="currOffset" [limit]="1" [range]="1" [size]="getPages(totalCount, pageSize)"
                (pageChange)="onPageChange($event)" [isV2Pagination]="true">
              </pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>




<ng-template #loadingTemplate>
  <div class="flex-center h-360">
    <application-loader></application-loader>
  </div>
</ng-template>



<ng-template #subSourcesPopup>
  <div class="p-20">
    <div class="align-center justify-between mb-3">
      <h4 class="m-0">Sub-Sources ({{selectedSubSources?.length}})</h4>
      <div class="cursor-pointer flex-center header-5 fw-400 text-red-350" (click)="modalRef.hide()">
        <span class="ic-close ic-xxs mb-2 ic-red-350 mr-4"></span>Close
      </div>
    </div>
    <div class="d-flex flex-wrap">
      <div *ngFor="let subSource of selectedSubSources" class="mr-10 mb-10 bg-light-pearl p-10 rounded-pill d-flex align-items-center">
        <span class="text-coal">{{subSource}}</span>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #connectNowPopup>
  <div class="br-4">
    <h5 class="text-white fw-600 bg-black px-20 py-12">
      Connect {{currentSource?.displayName}} Source
    </h5>
    <div class="p-20">
      <div class="mb-16">
        <h6 class="fw-600 mb-12">Select Integration Method</h6>
        <div class="d-flex gap-3">
          <div class="flex-1 border br-8 p-16 cursor-pointer hover-bg-light-pearl"
               [ngClass]="{'bg-light-pearl border-primary': selectedIntegrationMethod === 'PUSH'}"
               (click)="selectedIntegrationMethod = 'PUSH'">
            <div class="d-flex align-items-center mb-8">
              <input type="radio" name="integrationMethod" value="PUSH"
                     [(ngModel)]="selectedIntegrationMethod" class="mr-8">
              <h6 class="fw-600 m-0">PUSH</h6>
            </div>
            <p class="text-sm text-muted m-0">Send data to your CRM automatically</p>
          </div>
          <div class="flex-1 border br-8 p-16 cursor-pointer hover-bg-light-pearl"
               [ngClass]="{'bg-light-pearl border-primary': selectedIntegrationMethod === 'PULL'}"
               (click)="selectedIntegrationMethod = 'PULL'">
            <div class="d-flex align-items-center mb-8">
              <input type="radio" name="integrationMethod" value="PULL"
                     [(ngModel)]="selectedIntegrationMethod" class="mr-8">
              <h6 class="fw-600 m-0">PULL</h6>
            </div>
            <p class="text-sm text-muted m-0">Fetch data from external source</p>
          </div>
        </div>
      </div>

      <div class="d-flex gap-2 justify-end">
        <button type="button" class="btn-gray" (click)="modalRef.hide()">Cancel</button>
        <button type="button" class="btn-coal" (click)="proceedWithIntegration()"
                [disabled]="!selectedIntegrationMethod">
          Continue
        </button>
      </div>
    </div>
  </div>
</ng-template>

