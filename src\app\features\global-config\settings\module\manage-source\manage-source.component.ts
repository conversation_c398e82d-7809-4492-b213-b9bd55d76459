import { ChangeDetectionStrategy, ChangeDetector<PERSON><PERSON>, <PERSON>mponent, <PERSON><PERSON><PERSON>ter, On<PERSON><PERSON>roy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { Title } from '@angular/platform-browser';

import { GridOptions } from 'ag-grid-community';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { AddSourceComponent } from './add-source/add-source.component';
import { PushIntegrationComponent } from './push-integration/push-integration.component';
import { PullIntegrationComponent } from './pull-integration/pull-integration.component';
import { SourceActionsComponent } from './source-actions/source-actions.component';
import { SourceToggleComponent } from './source-toggle/source-toggle.component';
import {
  ConvertToDirect,
  DeleteSource,
  FetchSources,
  UpdateSourceStatus
} from 'src/app/reducers/source/source.actions';
import {
  getIsToggleInProgress,
  getSources,
  getSourcesLoading
} from 'src/app/reducers/source/source.reducer';
import { getAssignedToDetails, getTimeZoneDate } from 'src/app/core/utils/common.util';
import { FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';
import { getUserBasicDetails, getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';

@Component({
  selector: 'manage-source',
  templateUrl: './manage-source.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ManageSourceComponent implements OnInit, OnDestroy {
  @ViewChild('sourceWarningPopup') sourceWarningPopup: TemplateRef<any>;
  @ViewChild('subSourcesPopup') subSourcesPopup: TemplateRef<any>;
  @ViewChild('connectNowPopup') connectNowPopup: TemplateRef<any>;
  @ViewChild('hideSourcePopup') hideSourcePopup: TemplateRef<any>;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  s3BucketUrl: string = 'https://leadrat-black.s3.ap-south-1.amazonaws.com/';


  sources: any[] = [];
  filteredSources: any[] = [];
  selectedSubSources: string[] = [];
  sourcesLoading: boolean = false;
  searchTerm: string = '';
  permissions: Set<unknown>;
  currentSource: any = null;
  isToggleInProgress: boolean = false;
  selectedIntegrationMethod: string = '';

  // User data for timezone and user list
  userData: any = null;
  allUsers: any[] = [];

  gridOptions: GridOptions;
  gridApi: any;
  gridColumnApi: any;

  currOffset: number = 0;
  pageSize: number = 10;
  selectedPageSize: number = 10;
  totalCount: number = 0;
  pageSizeOptions: number[] = [5, 10, 20, 50, 100];

  constructor(
    private store: Store<AppState>,
    private router: Router,
    private headerTitle: HeaderTitleService,
    private notificationService: NotificationsService,
    private modalService: BsModalService,
    public metaTitle: Title,
    private cdr: ChangeDetectorRef,
    public modalRef: BsModalRef,
    private gridOptionsService: GridOptionsService
  ) { }

  ngOnInit(): void {
    this.headerTitle.setLangTitle('Global Config');
    this.metaTitle.setTitle('CRM | Global Config');
    this.selectedPageSize = this.pageSize;
    this.initializeGridSettings();
    this.store.dispatch(new FetchSources());

    this.store
      .select(getSources)
      .pipe(takeUntil(this.stopper))
      .subscribe((sources: any[]) => {
        console.log('Sources from store:', sources);
        if (sources) {
          this.sources = sources;
          this.updateFilteredSources();
          this.cdr.markForCheck();
        }
      });

    this.store
      .select(getSourcesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.sourcesLoading = loading;
        this.cdr.markForCheck();
      });

    this.store
      .select(getIsToggleInProgress)
      .pipe(takeUntil(this.stopper))
      .subscribe((inProgress: boolean) => {
        this.isToggleInProgress = inProgress;
        this.cdr.markForCheck();
      });

    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        this.permissions = new Set(permissions);
        this.cdr.markForCheck();
      });

    // Fetch user data for timezone
    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((userData: any) => {
        this.userData = userData;
        this.cdr.markForCheck();
      });

    // Fetch users list for name resolution
    this.store.dispatch(new FetchUsersListForReassignment());
    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((users: any) => {
        this.allUsers = users || [];
        this.cdr.markForCheck();
      });

  }

  initializeGridSettings(): void {
    this.setupGridOptions();
    this.setupColumnDefinitions();
    this.gridOptions.context = {
      componentParent: this
    };
  }

  private setupGridOptions(): void {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.rowHeight = 60;
    this.gridOptions.headerHeight = 40;
    this.gridOptions.rowClass = 'custom-row source-grid-row';
    this.gridOptions.domLayout = 'normal';
    this.gridOptions.rowSelection = 'multiple';
    this.gridOptions.suppressRowClickSelection = true;
    this.gridOptions.suppressNoRowsOverlay = true;
    this.gridOptions.suppressLoadingOverlay = true;
    this.gridOptions.defaultColDef = {
      ...this.gridOptions.defaultColDef,
      cellStyle: {
        'line-height': '58px',
        'padding-left': '15px',
        'padding-right': '15px'
      },
      cellClass: 'source-grid-cell'
    };
    this.gridOptions.onSelectionChanged = this.onSelectionChanged.bind(this);
  }

  private setupColumnDefinitions(): void {
    this.gridOptions.columnDefs = [
      {
        headerName: 'Hide Source',
        field: 'toggle',
        maxWidth: 100,
        filter: false,
        sortable: false,
        lockPosition: true,
        valueGetter: (params: any) => [params.data?.isEnabled],
        cellRenderer: SourceToggleComponent,
        cellClass: 'source-toggle-cell'
      },
      {
        headerName: 'Source Logo',
        field: 'imageURL',
        maxWidth: 110,
        filter: false,
        sortable: false,
        valueGetter: (params: any) => params.data?.imageURL || '',
        cellRenderer: (params: any) => {
          return this.renderSourceLogo(params);
        },
        cellClass: 'source-logo-cell'
      },
      {
        headerName: 'Source Name',
        field: 'displayName',
        minWidth: 120,
        maxWidth: 150,
        filter: 'agTextColumnFilter',
        sortable: true,
        valueGetter: (params: any) => params.data?.displayName || params.data?.name || 'Unknown Source',
        cellRenderer: (params: any) => {
          return `<p class="source-name-text">${params.value || 'Unknown Source'}</p>`;
        },
        cellClass: 'source-name-cell'
      },
      {
        headerName: 'Sub Source',
        field: 'subSources',
        minWidth: 300,
        filter: 'agTextColumnFilter',
        sortable: true,
        valueGetter: (params: any) => params.data?.subSources || [],
        cellRenderer: (params: any) => {
          return this.renderSubSources(params);
        },
        onCellClicked: (params: any) => {
          this.handleSubSourceClick(params);
        },
        cellClass: 'sub-source-cell'
      },
      {
        headerName: 'Created',
        field: 'createdBy',
        minWidth: 200,
        filter: 'agTextColumnFilter',
        sortable: true,
        colId: 'CreatedOn',
        valueGetter: (params: any) => [
          getAssignedToDetails(params.data?.createdBy, this.allUsers, true) || '',
          params.data?.createdOn
            ? 'At ' +
            getTimeZoneDate(
              params.data?.createdOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'fullDateTime'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="fw-600 mb-4 created-by-name">${params.value[0]}</p>
            <p class="created-by-date">${params.value[1]}</p>
            <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic created-by-timezone">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[1]
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
        cellClass: 'created-by-cell'
      },
      {
        headerName: 'Modified',
        field: 'lastModifiedBy',
        minWidth: 200,
        filter: 'agTextColumnFilter',
        sortable: true,
        colId: 'LastModifiedOn',
        valueGetter: (params: any) => [
          getAssignedToDetails(
            params.data?.lastModifiedBy,
            this.allUsers,
            true
          ) || '',
          params.data?.lastModifiedOn
            ? 'At ' +
            getTimeZoneDate(
              params.data?.lastModifiedOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'fullDateTime'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="fw-600 mb-4 modified-by-name">${params.value[0]}</p>
            <p class="modified-by-date">${params.value[1]}</p>
            <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic modified-by-timezone">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[1]
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
        cellClass: 'modified-by-cell'
      },
      {
        headerName: 'Actions',
        field: 'Actions',
        minWidth: 180,
        maxWidth: 180,
        filter: false,
        sortable: false,
        suppressMovable: true,
        lockPosition: 'right',
        valueGetter: (params: any) => [params.data],
        cellRenderer: SourceActionsComponent,
        cellClass: 'source-actions-cell'
      }
    ];
  }

  updateFilteredSources(): void {
    let result = this.applySearchFilter([...this.sources]);
    result = this.applySorting(result);
    this.totalCount = result.length;
    this.filteredSources = this.applyPagination(result);
    this.updateGridData();
    this.cdr.markForCheck();
  }

  private applySearchFilter(sources: any[]): any[] {
    if (!this.searchTerm || this.searchTerm.trim() === '') {
      return sources;
    }
    const searchTermLower = this.searchTerm.toLowerCase().trim();
    return sources.filter(source => this.matchesSearchTerm(source, searchTermLower));
  }

  private matchesSearchTerm(source: any, searchTerm: string): boolean {
    const displayName = source.displayName || '';
    const name = source.name || '';
    const subSources = source.subSources || [];
    return displayName.toLowerCase().includes(searchTerm) ||
      name.toLowerCase().includes(searchTerm) ||
      subSources.some((subSource: string) => subSource.toLowerCase().includes(searchTerm));
  }

  private applySorting(sources: any[]): any[] {
    return sources.sort((a, b) => {
      // Enabled sources first
      if (a.isEnabled && !b.isEnabled) return -1;
      if (!a.isEnabled && b.isEnabled) return 1;
      // Then sort by display name
      return a.displayName.localeCompare(b.displayName);
    });
  }

  private applyPagination(sources: any[]): any[] {
    const startIndex = this.currOffset * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    return sources.slice(startIndex, endIndex);
  }

  private updateGridData(): void {
    if (this.gridApi) {
      this.gridApi.setRowData(this.filteredSources);
    }
  }



  getPages(totalCount: number, pageSize: number): number {
    return Math.ceil(totalCount / pageSize);
  }

  onPageChange(offset: number): void {
    this.currOffset = offset;
    this.updateFilteredSources();
  }

  onPageSizeChange(): void {
    this.pageSize = Number(this.selectedPageSize);
    this.currOffset = 0;
    this.updateFilteredSources();
  }

  onGridReady(params: any): void {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    if (this.filteredSources.length > 0) {
      this.gridApi.setRowData(this.filteredSources);
    }
    window.addEventListener('resize', () => {
      setTimeout(() => {
        if (this.gridApi) {
          this.gridApi.sizeColumnsToFit();
        }
      });
    });

    if (this.gridApi) {
      this.gridApi.sizeColumnsToFit();
    }
    setTimeout(() => {
      if (this.gridApi) {
        this.gridApi.refreshCells({ force: true });
      }
    }, 100);
  }

  onCellClicked(_event: any): void {
  }

  onSelectionChanged(_event: any): void {
    // Individual toggle selection - no bulk operations
    this.cdr.markForCheck();
  }

  toggleSourceVisibility(source: any): void {
    if (this.isToggleInProgress) {
      return;
    }
    this.currentSource = source;
    this.showToggleConfirmation(source);
  }

  showToggleConfirmation(source: any): void {
    const actionText = source.isEnabled ? 'disable' : 'enable';
    const initialState = {
      message: 'GLOBAL.user-confirmation',
      confirmType: actionText,
      title: source.displayName,
      fieldType: 'source',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      {
        class: 'modal-400 top-modal ph-modal-unset',
        initialState,
        ignoreBackdropClick: false,
      }
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason === 'confirmed') {
          this.confirmToggleSource();
        }
      });
    }
  }

  confirmToggleSource(): void {
    if (this.currentSource) {
      this.isToggleInProgress = true;
      this.cdr.markForCheck();
      // Create dummy payload for API call
      const payload = {
        sourceId: this.currentSource.id,
        isEnabled: !this.currentSource.isEnabled,
        sourceName: this.currentSource.displayName || this.currentSource.name
      };
      console.log('Dummy API Call - Toggle Source:', payload);
      // Dispatch action for toggle
      this.store.dispatch(new UpdateSourceStatus(this.currentSource.id, !this.currentSource.isEnabled));
    }
  }

  navigateToLead() {
    this.isToggleInProgress = false;
    this.cdr.markForCheck();
    if (this.modalRef) {
      this.modalRef.hide();
    }
    this.router.navigate(['leads/manage-leads'], {
      queryParams: {
        Source: JSON.stringify(this.currentSource.displayName),
        isNavigatedFromSource: true,
      },
    });
  }

  convertToDirect() {
    this.modalRef.hide();
    this.isToggleInProgress = true;
    this.cdr.markForCheck();
    if (this.currentSource) {
      const sourceValue = typeof this.currentSource.value === 'string'
        ? parseInt(this.currentSource.value, 10)
        : this.currentSource.value;
      this.store.dispatch(new ConvertToDirect(sourceValue));
    }
  }

  navigateToData() {
    this.isToggleInProgress = false;
    this.cdr.markForCheck();
    if (this.modalRef) {
      this.modalRef.hide();
    }
    this.router.navigate(['data/manage-data'], {
      queryParams: {
        SourceValue: this.currentSource.value,
        isNavigatedFromSource: true,
      },
    });
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.currOffset = 0;
    this.updateFilteredSources();
    this.cdr.markForCheck();
  }

  openAddSourceModal(): void {
    this.modalRef = this.modalService.show(
      AddSourceComponent,
      {
        class: 'modal-350 top-modal ph-modal-unset',
        ignoreBackdropClick: true
      }
    );
  }

  editSource(source: any): void {
    this.modalRef = this.modalService.show(
      AddSourceComponent,
      {
        class: 'modal-350 top-modal ph-modal-unset',
        initialState: { sourceData: source },
        ignoreBackdropClick: true
      }
    );
  }

  deleteSource(source: any): void {
    if (source.isMasterSource) {
      this.notificationService.error('Master sources cannot be deleted.');
      return;
    }

    this.currentSource = source;
    this.showDeleteConfirmation(source);
  }

  openConnectNowModal(source: any): void {
    this.currentSource = source;
    this.selectedIntegrationMethod = '';
    this.modalRef = this.modalService.show(
      this.connectNowPopup,
      {
        class: 'modal-600 top-modal ph-modal-unset',
        ignoreBackdropClick: true
      }
    );
  }

  toggleIntegration(source: any): void {
    // This will enable the source for integration and make it appear in Global Config > Integration
    this.notificationService.info(`Integration toggle for ${source.displayName} will be implemented.`);
    // TODO: Implement integration toggle functionality
  }

  proceedWithIntegration(): void {
    if (!this.selectedIntegrationMethod || !this.currentSource) {
      return;
    }
    this.modalRef.hide();
    // Navigate to integration setup based on selected method
    if (this.selectedIntegrationMethod === 'PUSH') {
      this.openPushIntegrationModal();
    } else if (this.selectedIntegrationMethod === 'PULL') {
      this.openPullIntegrationModal();
    }
  }

  openPushIntegrationModal(): void {
    this.modalRef = this.modalService.show(
      PushIntegrationComponent,
      {
        class: 'modal-550 right-modal ip-modal-unset',
        initialState: { sourceData: this.currentSource },
        ignoreBackdropClick: true
      }
    );
  }
  openPullIntegrationModal(): void {
    this.modalRef = this.modalService.show(
      PullIntegrationComponent,
      {
        class: 'modal-550 right-modal ip-modal-unset',
        initialState: { sourceData: this.currentSource },
        ignoreBackdropClick: true
      }
    );
  }

  showDeleteConfirmation(source: any): void {
    const initialState = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: source.displayName,
      fieldType: 'source',
    };

    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      {
        class: 'modal-400 top-modal ph-modal-unset',
        initialState,
        ignoreBackdropClick: false,
      }
    );

    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason === 'confirmed') {
          this.confirmDeleteSource();
        }
      });
    }
  }

  confirmDeleteSource(): void {
    if (this.currentSource) {
      this.isToggleInProgress = true;
      this.cdr.markForCheck();
      // Create dummy payload for delete API call
      const payload = {
        sourceId: this.currentSource.id,
        sourceName: this.currentSource.displayName || this.currentSource.name
      };
      console.log('Dummy API Call - Delete Source:', payload);
      // Dispatch delete action
      this.store.dispatch(new DeleteSource(this.currentSource.id));
    }
  }

  goBack(): void {
    this.router.navigate(['/global-config']);
  }

  showAllSubSources(subSources: string[]): void {
    this.selectedSubSources = subSources || [];
    this.modalRef = this.modalService.show(
      this.subSourcesPopup,
      {
        class: 'modal-400 modal-dialog-centered ph-modal-unset',
        ignoreBackdropClick: false
      }
    );
  }

  renderSourceLogo(params: any): string {
    const sourceName = params.data?.displayName || params.data?.name || '';
    const firstChar = sourceName.charAt(0).toUpperCase();
    const bgColor = this.getRandomColor(sourceName);
    const imageUrl = params.value || '';
    // Check if valid image URL exists
    const hasValidImage = this.hasValidImageUrl(imageUrl);
    const fullImageUrl = this.getImageUrl(imageUrl);
    if (hasValidImage) {
      return `
        <div class="d-flex align-items-center source-logo-container">
          <img src="${fullImageUrl}" alt="${sourceName}" width="30" height="30" class="mr-2 rounded source-logo-image"
               onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
          <div class="source-logo-fallback" style="display:none; width:30px; height:30px; background-color:${bgColor}; color:white;
               border-radius:4px; justify-content:center; align-items:center; font-weight:bold; margin-right:8px;">
            <span class="source-logo-char">${firstChar}</span>
          </div>
        </div>`;
    } else {
      return `
        <div class="d-flex align-items-center source-logo-container">
          <div class="source-logo-placeholder" style="display:flex; width:30px; height:30px; background-color:${bgColor}; color:white;
               border-radius:4px; justify-content:center; align-items:center; font-weight:bold; margin-right:8px;">
            <span class="source-logo-char">${firstChar}</span>
          </div>
        </div>`;
    }
  }

  renderSubSources(params: any): string {
    const subSources: string[] = params.value || [];
    if (subSources.length === 0) {
      return '<span class="text-muted sub-source-empty">No sub-sources</span>';
    }
    const visibleSubSources = subSources.slice(0, 2);
    const remainingCount = subSources.length - 2;
    const subSourceTags = visibleSubSources.map(subSource =>
      `<div class="bg-light-pearl mr-8 px-8 py-4 rounded-pill sub-source-tag">
        <span class="text-coal text-sm sub-source-text">${subSource}</span>
      </div>`
    ).join('');
    if (remainingCount > 0) {
      return `
        <div class="d-flex align-items-center sub-source-container">
          ${subSourceTags}
          <div class="bg-accent-green d-flex align-items-center justify-content-center px-8 py-4 rounded-pill text-white cursor-pointer sub-source-more">
            <span class="text-decoration-underline text-sm sub-source-more-text" data-action="showMore">+${remainingCount} more</span>
          </div>
        </div>`;
    } else {
      return `<div class="d-flex align-items-center sub-source-container">${subSourceTags}</div>`;
    }
  }

  handleSubSourceClick(params: any): void {
    const action = params.event.target.getAttribute('data-action');
    if (action === 'showMore') {
      const subSources: string[] = params.data?.subSources || [];
      this.showAllSubSources(subSources);
    }
  }

  private hasValidImageUrl(imageURL: string): boolean {
    try {
      return imageURL && imageURL.trim && imageURL.trim() !== '';
    } catch (error) {
      console.error('Error validating image URL:', error);
      return false;
    }
  }

  private getImageUrl(imageURL: string): string {
    if (!imageURL) return '';
    return imageURL.startsWith('http') ? imageURL : (this.s3BucketUrl || '') + imageURL;
  }

  getRandomColor(text: string): string {
    if (!text) return '#3498db';
    const colors = [
      '#3498db', // Blue
      '#2ecc71', // Green
      '#e74c3c', // Red
      '#f39c12', // Orange
      '#9b59b6', // Purple
      '#1abc9c', // Turquoise
      '#d35400', // Pumpkin
      '#c0392b', // Pomegranate
      '#16a085', // Green Sea
      '#8e44ad', // Wisteria
      '#27ae60', // Nephritis
      '#2980b9', // Belize Hole
      '#f1c40f', // Sunflower
      '#e67e22'  // Carrot
    ];
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      hash = text.charCodeAt(i) + ((hash << 5) - hash);
    }
    const index = Math.abs(hash) % colors.length;
    return colors[index];
  }

  ngOnDestroy(): void {
    this.stopper.next();
    this.stopper.complete();
  }
}
