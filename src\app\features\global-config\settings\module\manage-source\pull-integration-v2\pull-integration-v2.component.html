<div class="br-4">
  <h5 class="text-white fw-600 bg-black px-20 py-12">
    Setup PULL Integration for {{sourceData?.displayName}}
  </h5>
  
  <form [formGroup]="pullForm" autocomplete="off" class="p-20">
    <!-- Webhook URL -->
    <div class="mb-16">
      <div class="label-req">Webhook URL</div>
      <div class="d-flex">
        <input type="url" class="form-control" placeholder="Enter webhook URL" formControlName="webhookUrl">
        <button type="button" class="btn btn-outline-primary btn-sm ml-8" (click)="testWebhook()">
          Test Connection
        </button>
      </div>
    </div>

    <!-- Account Name -->
    <div class="mb-16">
      <div class="label-req">Account Name</div>
      <input type="text" class="form-control" placeholder="Enter account name" formControlName="accountName">
    </div>

    <!-- Login Id/Login Email -->
    <div class="mb-16">
      <div class="label">Login Id/Login Email</div>
      <input type="email" class="form-control" placeholder="Enter login email" formControlName="loginEmail">
    </div>

    <!-- Relationship Manager Email -->
    <div class="mb-16">
      <div class="label-req">Relationship Manager Email</div>
      <input type="email" class="form-control" placeholder="Enter relationship manager email" formControlName="relationshipManagerEmail">
    </div>

    <!-- Additional Email -->
    <div class="mb-16">
      <div class="label">Additional Email</div>
      <input type="email" class="form-control" placeholder="Enter additional email" formControlName="additionalEmail">
    </div>

    <!-- BCC Email -->
    <div class="mb-16">
      <div class="label">BCC Email</div>
      <input type="email" class="form-control" placeholder="Enter BCC email" formControlName="bccEmail">
    </div>

    <!-- Import cURL -->
    <div class="mb-16">
      <div class="label-req">Import cURL</div>
      <textarea class="form-control" rows="4" placeholder="Paste your cURL command here..." formControlName="curlCommand"></textarea>
      <div class="mt-8">
        <button type="button" class="btn btn-outline-primary btn-sm" (click)="extractFields()" [disabled]="isExtractingFields">
          <span *ngIf="isExtractingFields" class="spinner-border spinner-border-sm mr-2"></span>
          Extract Fields
        </button>
      </div>
    </div>

    <!-- Payload Mapping -->
    <div class="mb-16">
      <div class="label-req">Payload Mapping</div>
      <div class="border p-12 br-4">
        <div formArrayName="payloadMapping">
          <div *ngFor="let mapping of payloadMappingArray.controls; let i = index" 
               [formGroupName]="i" class="d-flex align-items-center mb-8">
            <div class="flex-1 mr-8">
              <select class="form-control" formControlName="sourceField">
                <option value="">Select Source Field</option>
                <option *ngFor="let field of availableFields" [value]="field.value">{{field.label}}</option>
              </select>
            </div>
            <div class="flex-1 mr-8">
              <input type="text" class="form-control" placeholder="Target Field" formControlName="targetField">
            </div>
            <button type="button" class="btn btn-outline-danger btn-sm" (click)="removePayloadMapping(i)">
              <i class="fa fa-trash"></i>
            </button>
          </div>
        </div>
        <button type="button" class="btn btn-outline-primary btn-sm mt-8" (click)="addPayloadMapping()">
          + Add Additional Payload
        </button>
      </div>
    </div>

    <!-- Method Type -->
    <div class="mb-16">
      <div class="label-req">Method Type</div>
      <select class="form-control" formControlName="methodType">
        <option *ngFor="let method of methodTypes" [value]="method.value">{{method.label}}</option>
      </select>
    </div>

    <!-- Content Type -->
    <div class="mb-16">
      <div class="label-req">Content Type</div>
      <select class="form-control" formControlName="contentType">
        <option *ngFor="let type of contentTypes" [value]="type.value">{{type.label}}</option>
      </select>
    </div>

    <!-- Query Parameters -->
    <div class="mb-16">
      <div class="label">Query Parameters</div>
      <div class="border p-12 br-4">
        <div formArrayName="queryParameters">
          <div *ngFor="let param of queryParametersArray.controls; let i = index" 
               [formGroupName]="i" class="d-flex align-items-center mb-8">
            <div class="flex-1 mr-8">
              <input type="text" class="form-control" placeholder="Key" formControlName="key">
            </div>
            <div class="flex-1 mr-8">
              <input type="text" class="form-control" placeholder="Value" formControlName="value">
            </div>
            <button type="button" class="btn btn-outline-danger btn-sm" (click)="removeQueryParameter(i)">
              <i class="fa fa-trash"></i>
            </button>
          </div>
        </div>
        <button type="button" class="btn btn-outline-primary btn-sm mt-8" (click)="addQueryParameter()">
          + Add Query Parameters
        </button>
      </div>
    </div>

    <!-- Header Variables -->
    <div class="mb-16">
      <div class="label">Header Variables</div>
      <div class="border p-12 br-4">
        <div formArrayName="headerVariables">
          <div *ngFor="let header of headerVariablesArray.controls; let i = index" 
               [formGroupName]="i" class="d-flex align-items-center mb-8">
            <div class="flex-1 mr-8">
              <input type="text" class="form-control" placeholder="Key" formControlName="key">
            </div>
            <div class="flex-1 mr-8">
              <input type="text" class="form-control" placeholder="Value" formControlName="value">
            </div>
            <button type="button" class="btn btn-outline-danger btn-sm" (click)="removeHeaderVariable(i)">
              <i class="fa fa-trash"></i>
            </button>
          </div>
        </div>
        <button type="button" class="btn btn-outline-primary btn-sm mt-8" (click)="addHeaderVariable()">
          + Add Header Variables
        </button>
      </div>
    </div>

    <!-- Body Variables -->
    <div class="mb-16">
      <div class="label">Body Variables</div>
      <div class="border p-12 br-4">
        <div formArrayName="bodyVariables">
          <div *ngFor="let body of bodyVariablesArray.controls; let i = index" 
               [formGroupName]="i" class="d-flex align-items-center mb-8">
            <div class="flex-1 mr-8">
              <input type="text" class="form-control" placeholder="Key" formControlName="key">
            </div>
            <div class="flex-1 mr-8">
              <input type="text" class="form-control" placeholder="Value" formControlName="value">
            </div>
            <button type="button" class="btn btn-outline-danger btn-sm" (click)="removeBodyVariable(i)">
              <i class="fa fa-trash"></i>
            </button>
          </div>
        </div>
        <button type="button" class="btn btn-outline-primary btn-sm mt-8" (click)="addBodyVariable()">
          + Add Body Variables
        </button>
      </div>
    </div>

    <!-- Extracted Fields Display -->
    <div *ngIf="extractedFields.length > 0" class="mb-16">
      <div class="label">Extracted Fields</div>
      <div class="border p-12 br-4 bg-light">
        <div class="d-flex flex-wrap">
          <span *ngFor="let field of extractedFields" class="badge badge-secondary mr-8 mb-4">{{field}}</span>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="d-flex justify-content-end pt-16 border-top">
      <button type="button" class="btn btn-secondary mr-12" (click)="cancel()">Cancel</button>
      <button type="button" class="btn btn-primary" (click)="save()" [disabled]="pullForm.invalid">Add Account</button>
    </div>
  </form>
</div>
