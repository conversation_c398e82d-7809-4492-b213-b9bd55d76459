<div class="bg-dark w-100 px-16 py-12 text-white flex-between">
  <div class="align-center">
    <div class="icon ic-envelope-solid ic-large mr-8"></div>
    <h4 class="fw-semi-bold">API Email Integration - PULL</h4>
  </div>
  <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="cancel()"></div>
</div>
<div class="w-100">
  <div class="py-20">
    <form [formGroup]="pullForm" (keydown.enter)="$event.preventDefault()">
      <div class="px-16 ip-min-w-350 ip-max-w-350 h-100-130 scrollbar">
        <!-- Webhook URL -->
        <div class="field-label-req">Webhook URL</div>
        <form-errors-wrapper label="Webhook URL" [control]="pullForm.controls['webhookUrl']">
          <div class="flex-between position-relative">
            <input type="url" required formControlName="webhookUrl"
                   placeholder="Enter webhook URL" class="outline-0 padd-r pr-36" autocomplete="off" />
            <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md cursor-pointer"
                 (click)="testWebhook()">
              <span class="icon ic-connection ic-x-xs"></span>
            </div>
          </div>
        </form-errors-wrapper>

        <!-- Account Name -->
        <div>
          <div class="field-label-req">Account Name</div>
          <form-errors-wrapper label="Account Name" [control]="pullForm.controls['accountName']">
            <input type="text" required formControlName="accountName" name="accountName"
                   autocomplete="off" placeholder="ex. Manasa Pampana" />
          </form-errors-wrapper>
        </div>

        <!-- Login Id/Login Email -->
        <div class="field-label">Login Id/Login Email</div>
        <form-errors-wrapper label="Login Id/Login Email" [control]="pullForm.controls['loginEmail']">
          <input type="email" formControlName="loginEmail" placeholder="ex. <EMAIL>">
        </form-errors-wrapper>

        <!-- Relationship Manager Email -->
        <div class="field-label-req">Relationship Manager Email</div>
        <form-errors-wrapper label="Relationship Manager Email" [control]="pullForm.controls['relationshipManagerEmail']">
          <input type="email" required formControlName="relationshipManagerEmail"
                 placeholder="ex. <EMAIL>" autocomplete="off" />
        </form-errors-wrapper>

        <!-- Additional Email -->
        <div class="field-label">Additional Email</div>
        <form-errors-wrapper label="Additional Email" [control]="pullForm.controls['additionalEmail']">
          <input type="email" formControlName="additionalEmail" placeholder="ex. <EMAIL>">
        </form-errors-wrapper>

        <!-- BCC Email -->
        <div class="field-label">BCC Email</div>
        <form-errors-wrapper label="BCC Email" [control]="pullForm.controls['bccEmail']">
          <input type="email" formControlName="bccEmail" placeholder="ex. <EMAIL>">
        </form-errors-wrapper>

        <!-- Import cURL -->
        <div class="field-label-req">Import cURL</div>
        <form-errors-wrapper label="Import cURL" [control]="pullForm.controls['curlCommand']">
          <textarea formControlName="curlCommand" rows="4"
                    placeholder="Paste your cURL command here..." class="w-100"></textarea>
        </form-errors-wrapper>
        <div class="mt-8">
          <button type="button" class="btn-coal btn-sm" (click)="extractFields()" [disabled]="isExtractingFields">
            <span *ngIf="isExtractingFields" class="spinner-border spinner-border-sm mr-2"></span>
            Extract Fields
          </button>
        </div>

        <!-- Payload Mapping -->
        <div class="field-label-req">Payload Mapping</div>
        <div class="border p-12 br-4">
          <div formArrayName="payloadMapping">
            <div *ngFor="let mapping of payloadMappingArray.controls; let i = index"
                 [formGroupName]="i" class="d-flex align-items-center mb-8">
              <div class="flex-1 mr-8">
                <ng-select [items]="availableFields" bindLabel="label" bindValue="value"
                          placeholder="Select Source Field" class="bg-white"
                          formControlName="sourceField" [virtualScroll]="true">
                </ng-select>
              </div>
              <div class="flex-1 mr-8">
                <input type="text" formControlName="targetField" placeholder="Target Field" />
              </div>
              <div class="icon ic-close ic-gray cursor-pointer ml-6 text-red-hover"
                   (click)="removePayloadMapping(i)"></div>
            </div>
          </div>
          <button type="button" class="btn-coal btn-sm mt-8" (click)="addPayloadMapping()">
            + Add Additional Payload
          </button>
        </div>

        <!-- Method Type -->
        <div class="field-label-req">Method Type</div>
        <form-errors-wrapper label="Method Type" [control]="pullForm.controls['methodType']">
          <ng-select [items]="methodTypes" bindLabel="label" bindValue="value"
                    placeholder="Select Method Type" class="bg-white"
                    formControlName="methodType" [virtualScroll]="true">
          </ng-select>
        </form-errors-wrapper>

        <!-- Content Type -->
        <div class="field-label-req">Content Type</div>
        <form-errors-wrapper label="Content Type" [control]="pullForm.controls['contentType']">
          <ng-select [items]="contentTypes" bindLabel="label" bindValue="value"
                    placeholder="Select Content Type" class="bg-white"
                    formControlName="contentType" [virtualScroll]="true">
          </ng-select>
        </form-errors-wrapper>

        <!-- Query Parameters -->
        <div class="field-label">Query Parameters</div>
        <div class="border p-12 br-4">
          <div formArrayName="queryParameters">
            <div *ngFor="let param of queryParametersArray.controls; let i = index"
                 [formGroupName]="i" class="d-flex align-items-center mb-8">
              <div class="flex-1 mr-8">
                <input type="text" formControlName="key" placeholder="Key" />
              </div>
              <div class="flex-1 mr-8">
                <input type="text" formControlName="value" placeholder="Value" />
              </div>
              <div class="icon ic-close ic-gray cursor-pointer ml-6 text-red-hover"
                   (click)="removeQueryParameter(i)"></div>
            </div>
          </div>
          <button type="button" class="btn-coal btn-sm mt-8" (click)="addQueryParameter()">
            + Add Query Parameters
          </button>
        </div>

        <!-- Header Variables -->
        <div class="field-label">Header Variables</div>
        <div class="border p-12 br-4">
          <div formArrayName="headerVariables">
            <div *ngFor="let header of headerVariablesArray.controls; let i = index"
                 [formGroupName]="i" class="d-flex align-items-center mb-8">
              <div class="flex-1 mr-8">
                <input type="text" formControlName="key" placeholder="Key" />
              </div>
              <div class="flex-1 mr-8">
                <input type="text" formControlName="value" placeholder="Value" />
              </div>
              <div class="icon ic-close ic-gray cursor-pointer ml-6 text-red-hover"
                   (click)="removeHeaderVariable(i)"></div>
            </div>
          </div>
          <button type="button" class="btn-coal btn-sm mt-8" (click)="addHeaderVariable()">
            + Add Header Variables
          </button>
        </div>

        <!-- Body Variables -->
        <div class="field-label">Body Variables</div>
        <div class="border p-12 br-4">
          <div formArrayName="bodyVariables">
            <div *ngFor="let body of bodyVariablesArray.controls; let i = index"
                 [formGroupName]="i" class="d-flex align-items-center mb-8">
              <div class="flex-1 mr-8">
                <input type="text" formControlName="key" placeholder="Key" />
              </div>
              <div class="flex-1 mr-8">
                <input type="text" formControlName="value" placeholder="Value" />
              </div>
              <div class="icon ic-close ic-gray cursor-pointer ml-6 text-red-hover"
                   (click)="removeBodyVariable(i)"></div>
            </div>
          </div>
          <button type="button" class="btn-coal btn-sm mt-8" (click)="addBodyVariable()">
            + Add Body Variables
          </button>
        </div>

        <!-- Extracted Fields Display -->
        <div *ngIf="extractedFields.length > 0" class="mt-16">
          <div class="field-label">Extracted Fields</div>
          <div class="border p-12 br-4 bg-pearl">
            <div class="d-flex flex-wrap">
              <span *ngFor="let field of extractedFields"
                    class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                <span class="fw-600 text-sm text-black-100">{{field}}</span>
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex-end p-16 box-shadow-20">
        <button type="button" class="btn-gray mr-20" (click)="cancel()">Cancel</button>
        <button type="button" class="btn-coal" (click)="save()" [disabled]="pullForm.invalid">
          Add Account
        </button>
      </div>
    </form>
  </div>
</div>
