import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { NotificationsService } from 'angular2-notifications';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { SourceService } from 'src/app/services/controllers/source.service';
import { AddSourceAccount } from 'src/app/reducers/source/source.actions';

@Component({
  selector: 'app-pull-integration-v2',
  templateUrl: './pull-integration-v2.component.html'
})
export class PullIntegrationV2Component implements OnInit {
  pullForm: FormGroup;
  sourceData: any;
  extractedFields: string[] = [];
  defaultPayloadMappings: any[] = [];
  availableFields: any[] = [];
  isExtractingFields: boolean = false;

  methodTypes = [
    { value: 'GET', label: 'GET' },
    { value: 'POST', label: 'POST' }
  ];

  contentTypes = [
    { value: 'form-data', label: 'form-data' },
    { value: 'x-www-form-urlencoded', label: 'x-www-form-urlencoded' },
    { value: 'application/json', label: 'application/json' }
  ];

  constructor(
    private fb: FormBuilder,
    public modalRef: BsModalRef,
    private notificationService: NotificationsService,
    private store: Store<AppState>,
    private sourceService: SourceService
  ) { }

  ngOnInit(): void {
    this.initForm();
    this.loadDefaultMappings();
  }

  initForm(): void {
    this.pullForm = this.fb.group({
      webhookUrl: ['', Validators.required],
      accountName: ['', Validators.required],
      loginId: [''],
      loginEmail: [''],
      relationshipManagerEmail: ['', [Validators.required, Validators.email]],
      additionalEmail: ['', Validators.email],
      bccEmail: ['', Validators.email],
      curlCommand: ['', Validators.required],
      methodType: ['GET', Validators.required],
      contentType: ['application/json', Validators.required],
      payloadMapping: this.fb.array([]),
      queryParameters: this.fb.array([]),
      headerVariables: this.fb.array([]),
      bodyVariables: this.fb.array([])
    });

    // Add default payload mappings
    this.addDefaultPayloadMappings();
  }

  get payloadMappingArray(): FormArray {
    return this.pullForm.get('payloadMapping') as FormArray;
  }

  get queryParametersArray(): FormArray {
    return this.pullForm.get('queryParameters') as FormArray;
  }

  get headerVariablesArray(): FormArray {
    return this.pullForm.get('headerVariables') as FormArray;
  }

  get bodyVariablesArray(): FormArray {
    return this.pullForm.get('bodyVariables') as FormArray;
  }

  createPayloadMappingGroup(sourceField: string = '', targetField: string = ''): FormGroup {
    return this.fb.group({
      sourceField: [sourceField, Validators.required],
      targetField: [targetField, Validators.required]
    });
  }

  createKeyValueGroup(key: string = '', value: string = ''): FormGroup {
    return this.fb.group({
      key: [key, Validators.required],
      value: [value, Validators.required]
    });
  }

  addPayloadMapping(): void {
    this.payloadMappingArray.push(this.createPayloadMappingGroup());
  }

  removePayloadMapping(index: number): void {
    this.payloadMappingArray.removeAt(index);
  }

  addQueryParameter(): void {
    this.queryParametersArray.push(this.createKeyValueGroup());
  }

  removeQueryParameter(index: number): void {
    this.queryParametersArray.removeAt(index);
  }

  addHeaderVariable(): void {
    this.headerVariablesArray.push(this.createKeyValueGroup());
  }

  removeHeaderVariable(index: number): void {
    this.headerVariablesArray.removeAt(index);
  }

  addBodyVariable(): void {
    this.bodyVariablesArray.push(this.createKeyValueGroup());
  }

  removeBodyVariable(index: number): void {
    this.bodyVariablesArray.removeAt(index);
  }

  addDefaultPayloadMappings(): void {
    // Pre-selected default mappings as per requirements
    const defaultMappings = [
      { sourceField: 'lead_name', targetField: 'Lead Name' },
      { sourceField: 'primary_no', targetField: 'Primary No' },
      { sourceField: 'secondary_no', targetField: 'Secondary No' }
    ];

    defaultMappings.forEach(mapping => {
      this.payloadMappingArray.push(
        this.createPayloadMappingGroup(mapping.sourceField, mapping.targetField)
      );
    });
  }

  extractFields(): void {
    const curlCommand = this.pullForm.get('curlCommand')?.value;
    if (!curlCommand) {
      this.notificationService.error('Please enter a cURL command first.');
      return;
    }

    this.isExtractingFields = true;
    this.sourceService.extractFieldsFromCurl(curlCommand).subscribe({
      next: (response) => {
        if (response && response.fields) {
          this.extractedFields = response.fields;
          this.notificationService.success('Fields extracted successfully.');
          this.updateAvailableFields();
        }
        this.isExtractingFields = false;
      },
      error: (error) => {
        this.notificationService.error('Failed to extract fields from cURL command.');
        this.isExtractingFields = false;
      }
    });
  }

  updateAvailableFields(): void {
    // Update available fields for mapping
    this.availableFields = this.extractedFields.map(field => ({
      value: field,
      label: field
    }));
  }

  loadDefaultMappings(): void {
    this.sourceService.getDefaultPayloadMappings().subscribe({
      next: (response) => {
        if (response && response.succeeded) {
          this.defaultPayloadMappings = response.data;
        }
      },
      error: (error) => {
        console.error('Failed to load default mappings:', error);
      }
    });
  }

  testWebhook(): void {
    const webhookUrl = this.pullForm.get('webhookUrl')?.value;
    if (!webhookUrl) {
      this.notificationService.error('Please enter webhook URL first.');
      return;
    }

    const testPayload = {
      test: true,
      timestamp: new Date().toISOString()
    };

    this.sourceService.testWebhookConnection(webhookUrl, testPayload).subscribe({
      next: (response) => {
        if (response && response.succeeded) {
          this.notificationService.success('Webhook connection test successful.');
        } else {
          this.notificationService.error('Webhook connection test failed.');
        }
      },
      error: (error) => {
        this.notificationService.error('Webhook connection test failed.');
      }
    });
  }

  save(): void {
    if (this.pullForm.invalid) {
      this.notificationService.error('Please fill all required fields.');
      return;
    }

    const formData = {
      sourceId: this.sourceData?.id,
      integrationMethod: 'PULL',
      ...this.pullForm.value
    };

    this.store.dispatch(new AddSourceAccount(formData));
    this.modalRef.hide();
  }

  cancel(): void {
    this.modalRef.hide();
  }
}
