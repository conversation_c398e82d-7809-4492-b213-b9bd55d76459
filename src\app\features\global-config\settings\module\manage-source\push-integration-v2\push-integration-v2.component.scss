.label-req {
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.label-req::after {
  content: ' *';
  color: #dc3545;
}

.label {
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.form-control {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
}

.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
}

.btn-outline-primary {
  color: #007bff;
  border-color: #007bff;
}

.btn-outline-primary:hover {
  background-color: #007bff;
  color: white;
}

.btn-outline-danger {
  color: #dc3545;
  border-color: #dc3545;
}

.btn-outline-danger:hover {
  background-color: #dc3545;
  color: white;
}

.badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.badge-secondary {
  background-color: #6c757d;
  color: white;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

.bg-light {
  background-color: #f8f9fa !important;
}

.border-top {
  border-top: 1px solid #dee2e6 !important;
}

.flex-1 {
  flex: 1;
}

.br-4 {
  border-radius: 4px;
}

.bg-black {
  background-color: #000 !important;
}
