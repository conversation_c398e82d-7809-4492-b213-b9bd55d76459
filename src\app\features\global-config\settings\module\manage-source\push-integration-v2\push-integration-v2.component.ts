import { Component, OnInit } from '@angular/core';
import { Form<PERSON>rray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { NotificationsService } from 'angular2-notifications';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { SourceService } from 'src/app/services/controllers/source.service';
import { AddSourceAccount } from 'src/app/reducers/source/source.actions';

@Component({
  selector: 'app-push-integration-v2',
  templateUrl: './push-integration-v2.component.html'
})
export class PushIntegrationV2Component implements OnInit {
  pushForm: FormGroup;
  sourceData: any;
  extractedFields: string[] = [];
  defaultPayloadMappings: any[] = [];
  availableFields: any[] = [];
  isExtractingFields: boolean = false;

  constructor(
    private fb: FormBuilder,
    public modalRef: BsModalRef,
    private notificationService: NotificationsService,
    private store: Store<AppState>,
    private sourceService: SourceService
  ) { }

  ngOnInit(): void {
    this.initForm();
    this.loadDefaultMappings();
  }

  initForm(): void {
    this.pushForm = this.fb.group({
      accountName: ['', Validators.required],
      loginId: [''],
      loginEmail: [''],
      relationshipManagerEmail: ['', [Validators.required, Validators.email]],
      additionalEmail: ['', Validators.email],
      bccEmail: ['', Validators.email],
      curlCommand: ['', Validators.required],
      payloadMapping: this.fb.array([])
    });

    // Add default payload mappings
    this.addDefaultPayloadMappings();
  }

  get payloadMappingArray(): FormArray {
    return this.pushForm.get('payloadMapping') as FormArray;
  }

  createPayloadMappingGroup(sourceField: string = '', targetField: string = ''): FormGroup {
    return this.fb.group({
      sourceField: [sourceField, Validators.required],
      targetField: [targetField, Validators.required]
    });
  }

  addPayloadMapping(): void {
    this.payloadMappingArray.push(this.createPayloadMappingGroup());
  }

  removePayloadMapping(index: number): void {
    this.payloadMappingArray.removeAt(index);
  }

  addDefaultPayloadMappings(): void {
    // Pre-selected default mappings as per requirements
    const defaultMappings = [
      { sourceField: 'lead_name', targetField: 'Lead Name' },
      { sourceField: 'primary_no', targetField: 'Primary No' },
      { sourceField: 'secondary_no', targetField: 'Secondary No' }
    ];

    defaultMappings.forEach(mapping => {
      this.payloadMappingArray.push(
        this.createPayloadMappingGroup(mapping.sourceField, mapping.targetField)
      );
    });
  }

  extractFields(): void {
    const curlCommand = this.pushForm.get('curlCommand')?.value;
    if (!curlCommand) {
      this.notificationService.error('Please enter a cURL command first.');
      return;
    }

    this.isExtractingFields = true;
    this.sourceService.extractFieldsFromCurl(curlCommand).subscribe({
      next: (response) => {
        if (response && response.fields) {
          this.extractedFields = response.fields;
          this.notificationService.success('Fields extracted successfully.');
          this.updateAvailableFields();
        }
        this.isExtractingFields = false;
      },
      error: (error) => {
        this.notificationService.error('Failed to extract fields from cURL command.');
        this.isExtractingFields = false;
      }
    });
  }

  updateAvailableFields(): void {
    // Update available fields for mapping
    this.availableFields = this.extractedFields.map(field => ({
      value: field,
      label: field
    }));
  }

  loadDefaultMappings(): void {
    this.sourceService.getDefaultPayloadMappings().subscribe({
      next: (response) => {
        if (response && response.succeeded) {
          this.defaultPayloadMappings = response.data;
        }
      },
      error: (error) => {
        console.error('Failed to load default mappings:', error);
      }
    });
  }

  save(): void {
    if (this.pushForm.invalid) {
      this.notificationService.error('Please fill all required fields.');
      return;
    }

    const formData = {
      sourceId: this.sourceData?.id,
      integrationMethod: 'PUSH',
      ...this.pushForm.value
    };

    this.store.dispatch(new AddSourceAccount(formData));
    this.modalRef.hide();
  }

  cancel(): void {
    this.modalRef.hide();
  }
}
