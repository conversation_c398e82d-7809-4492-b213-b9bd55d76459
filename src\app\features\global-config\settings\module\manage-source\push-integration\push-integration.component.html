<div class="br-4">
  <h5 class="text-white fw-600 bg-black px-20 py-12">
    Setup PUSH Integration for {{sourceData?.displayName}}
  </h5>
  
  <form [formGroup]="pushForm" autocomplete="off" class="p-20">
    <!-- Account Name -->
    <div class="mb-16">
      <div class="label-req">Account Name</div>
      <input type="text" class="form-control" placeholder="Enter account name" formControlName="accountName">
    </div>

    <!-- Login ID/Email -->
    <div class="row mb-16">
      <div class="col-md-6">
        <div class="label">Login ID</div>
        <input type="text" class="form-control" placeholder="Enter login ID" formControlName="loginId">
      </div>
      <div class="col-md-6">
        <div class="label">Login Email</div>
        <input type="email" class="form-control" placeholder="Enter login email" formControlName="loginEmail">
      </div>
    </div>

    <!-- Relationship Manager Email -->
    <div class="mb-16">
      <div class="label-req">Relationship Manager Email</div>
      <input type="email" class="form-control" placeholder="Enter relationship manager email" formControlName="relationshipManagerEmail">
    </div>

    <!-- Additional Email & BCC Email -->
    <div class="row mb-16">
      <div class="col-md-6">
        <div class="label">Additional Email</div>
        <input type="email" class="form-control" placeholder="Enter additional email" formControlName="additionalEmail">
      </div>
      <div class="col-md-6">
        <div class="label">BCC Email</div>
        <input type="email" class="form-control" placeholder="Enter BCC email" formControlName="bccEmail">
      </div>
    </div>

    <!-- Import cURL -->
    <div class="mb-16">
      <div class="label-req">Import cURL</div>
      <textarea class="form-control" rows="4" placeholder="Paste your cURL command here..." formControlName="curlCommand"></textarea>
      <div class="mt-8">
        <button type="button" class="btn btn-outline-primary btn-sm" (click)="extractFields()" [disabled]="isExtractingFields">
          <span *ngIf="isExtractingFields" class="spinner-border spinner-border-sm mr-2"></span>
          Extract Fields
        </button>
      </div>
    </div>

    <!-- Extracted Fields Display -->
    <div *ngIf="extractedFields.length > 0" class="mb-16">
      <div class="label">Extracted Fields</div>
      <div class="bg-light p-12 br-4">
        <div class="d-flex flex-wrap">
          <span *ngFor="let field of extractedFields" class="badge bg-primary text-white mr-8 mb-8">{{field}}</span>
        </div>
      </div>
    </div>

    <!-- Payload Mapping -->
    <div class="mb-16">
      <div class="label-req">Payload Mapping</div>
      <p class="text-sm text-muted mb-12">Map the fields from your source to the target system. Some fields are pre-selected by default.</p>
      
      <div formArrayName="payloadMapping">
        <div *ngFor="let mapping of payloadMappingArray.controls; let i = index" [formGroupName]="i" class="border p-12 mb-8 br-4">
          <div class="row align-items-center">
            <div class="col-md-4">
              <div class="label">Source Field</div>
              <select class="form-control" formControlName="sourceField">
                <option value="">Select source field</option>
                <option *ngFor="let field of availableFields" [value]="field.value">{{field.label}}</option>
              </select>
            </div>
            <div class="col-md-4">
              <div class="label">Target Field</div>
              <input type="text" class="form-control" placeholder="Enter target field" formControlName="targetField">
            </div>
            <div class="col-md-2">
              <div class="form-check">
                <input type="checkbox" class="form-check-input" formControlName="isRequired">
                <label class="form-check-label">Required</label>
              </div>
            </div>
            <div class="col-md-2">
              <button type="button" class="btn btn-outline-danger btn-sm" (click)="removePayloadMapping(i)" 
                      [disabled]="payloadMappingArray.length <= 1">
                <span class="icon ic-trash ic-xs"></span>
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <button type="button" class="btn btn-outline-primary btn-sm mt-8" (click)="addPayloadMapping()">
        <span class="icon ic-add ic-xs mr-4"></span>
        Add Additional Payload
      </button>
    </div>

    <!-- Action Buttons -->
    <div class="d-flex gap-2 justify-end mt-20">
      <button type="button" class="btn-gray" (click)="cancel()">Cancel</button>
      <button type="button" class="btn-coal" (click)="save()" [disabled]="pushForm.invalid">
        Add Account
      </button>
    </div>
  </form>
</div>
