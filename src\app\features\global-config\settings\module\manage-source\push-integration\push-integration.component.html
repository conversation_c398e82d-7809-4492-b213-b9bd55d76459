<div class="bg-dark w-100 px-16 py-12 text-white flex-between">
  <div class="align-center">
    <div class="icon ic-envelope-solid ic-large mr-8"></div>
    <h4 class="fw-semi-bold">API Email Integration - PUSH</h4>
  </div>
  <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="cancel()"></div>
</div>
<div class="w-100">
  <div class="py-20">
    <form [formGroup]="pushForm" (keydown.enter)="$event.preventDefault()">
      <div class="px-16 ip-min-w-350 ip-max-w-350 h-100-130 scrollbar">
        <!-- Account Name -->
        <div>
          <div class="field-label-req">Account Name</div>
          <form-errors-wrapper label="Account Name" [control]="pushForm.controls['accountName']">
            <input type="text" required formControlName="accountName" name="accountName"
                   autocomplete="off" placeholder="ex. Manasa Pampana" />
          </form-errors-wrapper>
        </div>

        <!-- Login Id/Login Email -->
        <div class="field-label">Login Id/Login Email</div>
        <form-errors-wrapper label="Login Id/Login Email" [control]="pushForm.controls['loginEmail']">
          <input type="email" formControlName="loginEmail" placeholder="ex. <EMAIL>">
        </form-errors-wrapper>

        <!-- Relationship Manager Email -->
        <div class="field-label-req">Relationship Manager Email</div>
        <form-errors-wrapper label="Relationship Manager Email" [control]="pushForm.controls['relationshipManagerEmail']">
          <input type="email" required formControlName="relationshipManagerEmail"
                 placeholder="ex. <EMAIL>" autocomplete="off" />
        </form-errors-wrapper>

        <!-- Additional Email -->
        <div class="field-label">Additional Email</div>
        <form-errors-wrapper label="Additional Email" [control]="pushForm.controls['additionalEmail']">
          <input type="email" formControlName="additionalEmail" placeholder="ex. <EMAIL>">
        </form-errors-wrapper>

        <!-- BCC Email -->
        <div class="field-label">BCC Email</div>
        <form-errors-wrapper label="BCC Email" [control]="pushForm.controls['bccEmail']">
          <input type="email" formControlName="bccEmail" placeholder="ex. <EMAIL>">
        </form-errors-wrapper>

        <!-- Import cURL -->
        <div class="field-label-req">Import cURL</div>
        <form-errors-wrapper label="Import cURL" [control]="pushForm.controls['curlCommand']">
          <textarea formControlName="curlCommand" rows="4"
                    placeholder="Paste your cURL command here..." class="w-100"></textarea>
        </form-errors-wrapper>
        <div class="mt-8">
          <button type="button" class="btn-coal btn-sm" (click)="extractFields()" [disabled]="isExtractingFields">
            <span *ngIf="isExtractingFields" class="spinner-border spinner-border-sm mr-2"></span>
            Extract Fields
          </button>
        </div>

        <!-- Payload Mapping -->
        <div class="field-label-req">Payload Mapping</div>
        <div class="border p-12 br-4">
          <div formArrayName="payloadMapping">
            <div *ngFor="let mapping of payloadMappingArray.controls; let i = index"
                 [formGroupName]="i" class="d-flex align-items-center mb-8">
              <div class="flex-1 mr-8">
                <ng-select [items]="availableFields" bindLabel="label" bindValue="value"
                          placeholder="Select Source Field" class="bg-white"
                          formControlName="sourceField" [virtualScroll]="true">
                </ng-select>
              </div>
              <div class="flex-1 mr-8">
                <input type="text" formControlName="targetField" placeholder="Target Field" />
              </div>
              <div class="icon ic-close ic-gray cursor-pointer ml-6 text-red-hover"
                   (click)="removePayloadMapping(i)"></div>
            </div>
          </div>
          <button type="button" class="btn-coal btn-sm mt-8" (click)="addPayloadMapping()">
            + Add Additional Payload
          </button>
        </div>

        <!-- Extracted Fields Display -->
        <div *ngIf="extractedFields.length > 0" class="mt-16">
          <div class="field-label">Extracted Fields</div>
          <div class="border p-12 br-4 bg-pearl">
            <div class="d-flex flex-wrap">
              <span *ngFor="let field of extractedFields"
                    class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                <span class="fw-600 text-sm text-black-100">{{field}}</span>
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex-end p-16 box-shadow-20">
        <button type="button" class="btn-gray mr-20" (click)="cancel()">Cancel</button>
        <button type="button" class="btn-coal" (click)="save()" [disabled]="pushForm.invalid">
          Add Account
        </button>
      </div>
    </form>
  </div>
</div>
