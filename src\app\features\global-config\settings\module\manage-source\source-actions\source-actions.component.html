<div class="align-center">
  <!-- Edit Action -->
  <div title="Edit" class="bg-blue-850 icon-badge" (click)="editSource()">
    <span class="icon ic-pen-solid m-auto ic-xxs" id="clkEditLead" data-automate-id="clkEditLead">
    </span>
  </div>

  <!-- Delete - Only for non-master sources -->
  <div *ngIf="!params.data?.isMasterSource"
       title="Delete"
       class="bg-light-red icon-badge"
       (click)="deleteSource()">
    <span class="icon ic-trash ic-xxxs"></span>
  </div>

  <!-- Hide/Show Toggle -->
  <div [title]="params.data?.isEnabled ? 'Hide Source' : 'Show Source'"
       [ngClass]="params.data?.isEnabled ? 'bg-orange-500' : 'bg-green-500'"
       class="icon-badge"
       (click)="toggleSourceVisibility()">
    <span [ngClass]="params.data?.isEnabled ? 'ic-eye-slash' : 'ic-eye'"
          class="icon m-auto ic-xxs">
    </span>
  </div>

  <!-- Enable/Disable Toggle for Integration -->
  <div *ngIf="params.data?.isEnabled"
       title="Enable for Integration"
       class="bg-accent-green icon-badge"
       (click)="toggleIntegration()">
    <span class="icon ic-toggle-on ic-xxs"></span>
  </div>
  <div *ngIf="!params.data?.isEnabled"
       title="Disabled for Integration"
       class="bg-gray-400 icon-badge">
    <span class="icon ic-toggle-off ic-xxs"></span>
  </div>
</div>