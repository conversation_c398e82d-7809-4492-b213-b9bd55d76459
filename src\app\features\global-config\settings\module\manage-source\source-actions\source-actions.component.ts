import { Component } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { ICellRendererParams } from 'ag-grid-community';

@Component({
  selector: 'source-actions',
  templateUrl: './source-actions.component.html'
})
export class SourceActionsComponent implements ICellRendererAngularComp {
  params: any;

  constructor() { }

  agInit(params: ICellRendererParams): void {
    this.params = params;
  }

  refresh(params: ICellRendererParams): boolean {
    return false;
  }

  editSource(): void {
    if (this.params.context.componentParent.editSource) {
      this.params.context.componentParent.editSource(this.params.data);
    }
  }

  deleteSource(): void {
    if (this.params.context.componentParent.deleteSource) {
      this.params.context.componentParent.deleteSource(this.params.data);
    }
  }

  toggleSourceVisibility(): void {
    if (this.params.context.componentParent.toggleSourceVisibility) {
      this.params.context.componentParent.toggleSourceVisibility(this.params.data);
    }
  }

  connectNow(): void {
    if (this.params.context.componentParent.openConnectNowModal) {
      this.params.context.componentParent.openConnectNowModal(this.params.data);
    }
  }

  toggleIntegration(): void {
    if (this.params.context.componentParent.toggleIntegration) {
      this.params.context.componentParent.toggleIntegration(this.params.data);
    }
  }
}
