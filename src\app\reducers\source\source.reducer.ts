import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { SourceActions, SourceActionTypes } from './source.actions';

export interface SourceState {
  sources: any[];
  sourcesLoading: boolean;
  error: any;
  sourceCountData: any;
  sourceCountDataLoading: boolean;
  isToggleInProgress: boolean;
  isAddingSource: boolean;
  isUpdatingSource: boolean;
  isDeletingSource: boolean;
  isConvertingToDirect: boolean;
  doesExistSourceName: boolean;
  // New state properties
  sourceAccounts: any[];
  sourceAccountsLoading: boolean;
  isAddingAccount: boolean;
  isUpdatingAccount: boolean;
  isDeletingAccount: boolean;
  isEnablingIntegration: boolean;
  extractedFields: any;
  isExtractingFields: boolean;
  subSourceValidation: { isValid: boolean; message?: string } | null;
  isValidatingSubSource: boolean;
}

export const initialState: SourceState = {
  sources: [],
  sourcesLoading: false,
  error: null,
  sourceCountData: null,
  sourceCountDataLoading: false,
  isToggleInProgress: false,
  isAddingSource: false,
  isUpdatingSource: false,
  isDeletingSource: false,
  isConvertingToDirect: false,
  doesExistSourceName: false,
  // New initial state properties
  sourceAccounts: [],
  sourceAccountsLoading: false,
  isAddingAccount: false,
  isUpdatingAccount: false,
  isDeletingAccount: false,
  isEnablingIntegration: false,
  extractedFields: null,
  isExtractingFields: false,
  subSourceValidation: null,
  isValidatingSubSource: false
};

export function sourceReducer(
  state: SourceState = initialState,
  action: Action
): SourceState {
  switch (action.type) {
    // Fetch Sources
    case SourceActionTypes.FETCH_SOURCES:
      return {
        ...state,
        sourcesLoading: true,
        error: null
      };
    case SourceActionTypes.FETCH_SOURCES_SUCCESS:
      return {
        ...state,
        sources: (action as any).payload,
        sourcesLoading: false
      };
    case SourceActionTypes.FETCH_SOURCES_FAILURE:
      return {
        ...state,
        error: (action as any).payload,
        sourcesLoading: false
      };

    // Add Source
    case SourceActionTypes.ADD_SOURCE:
      return {
        ...state,
        isAddingSource: true,
        error: null
      };
    case SourceActionTypes.ADD_SOURCE_SUCCESS:
      return {
        ...state,
        isAddingSource: false
      };
    case SourceActionTypes.ADD_SOURCE_FAILURE:
      return {
        ...state,
        error: (action as any).payload,
        isAddingSource: false
      };

    // Update Source
    case SourceActionTypes.UPDATE_SOURCE:
      return {
        ...state,
        isUpdatingSource: true,
        error: null
      };
    case SourceActionTypes.UPDATE_SOURCE_SUCCESS:
      return {
        ...state,
        isUpdatingSource: false
      };
    case SourceActionTypes.UPDATE_SOURCE_FAILURE:
      return {
        ...state,
        error: (action as any).payload,
        isUpdatingSource: false
      };

    // Delete Source
    case SourceActionTypes.DELETE_SOURCE:
      return {
        ...state,
        isDeletingSource: true,
        error: null
      };
    case SourceActionTypes.DELETE_SOURCE_SUCCESS:
      return {
        ...state,
        isDeletingSource: false
      };
    case SourceActionTypes.DELETE_SOURCE_FAILURE:
      return {
        ...state,
        error: (action as any).payload,
        isDeletingSource: false
      };

    // Update Source Status
    case SourceActionTypes.UPDATE_SOURCE_STATUS:
      return {
        ...state,
        isToggleInProgress: true,
        error: null
      };
    case SourceActionTypes.UPDATE_SOURCE_STATUS_SUCCESS:
      return {
        ...state,
        isToggleInProgress: false
      };
    case SourceActionTypes.UPDATE_SOURCE_STATUS_FAILURE:
      return {
        ...state,
        error: (action as any).payload,
        isToggleInProgress: false
      };

    // Bulk Update Source Status
    case SourceActionTypes.BULK_UPDATE_SOURCE_STATUS:
      return {
        ...state,
        isToggleInProgress: true,
        error: null
      };
    case SourceActionTypes.BULK_UPDATE_SOURCE_STATUS_SUCCESS:
      return {
        ...state,
        isToggleInProgress: false
      };
    case SourceActionTypes.BULK_UPDATE_SOURCE_STATUS_FAILURE:
      return {
        ...state,
        error: (action as any).payload,
        isToggleInProgress: false
      };

    // Get Lead and Data Count
    case SourceActionTypes.GET_LEAD_DATA_COUNT:
      return {
        ...state,
        sourceCountDataLoading: true,
        error: null
      };
    case SourceActionTypes.GET_LEAD_DATA_COUNT_SUCCESS:
      return {
        ...state,
        sourceCountData: (action as any).payload,
        sourceCountDataLoading: false
      };
    case SourceActionTypes.GET_LEAD_DATA_COUNT_FAILURE:
      return {
        ...state,
        error: (action as any).payload,
        sourceCountDataLoading: false
      };

    // Convert to Direct
    case SourceActionTypes.CONVERT_TO_DIRECT:
      return {
        ...state,
        isConvertingToDirect: true,
        error: null
      };
    case SourceActionTypes.CONVERT_TO_DIRECT_SUCCESS:
      return {
        ...state,
        isConvertingToDirect: false
      };
    case SourceActionTypes.CONVERT_TO_DIRECT_FAILURE:
      return {
        ...state,
        error: (action as any).payload,
        isConvertingToDirect: false
      };

      case SourceActionTypes.EXIST_SOURCE:
      return {
        ...state,
        error: null
      };
    case SourceActionTypes.EXIST_SOURCE_SUCCESS:
      return {
        ...state,
        doesExistSourceName: (action as any).response
      };

    // Enable Source Integration
    case SourceActionTypes.ENABLE_SOURCE_INTEGRATION:
      return {
        ...state,
        isEnablingIntegration: true
      };
    case SourceActionTypes.ENABLE_SOURCE_INTEGRATION_SUCCESS:
      return {
        ...state,
        isEnablingIntegration: false
      };

    // Add Source Account
    case SourceActionTypes.ADD_SOURCE_ACCOUNT:
      return {
        ...state,
        isAddingAccount: true
      };
    case SourceActionTypes.ADD_SOURCE_ACCOUNT_SUCCESS:
      return {
        ...state,
        isAddingAccount: false,
        sourceAccounts: (action as any).payload ? [...state.sourceAccounts, (action as any).payload] : state.sourceAccounts
      };

    // Get Source Accounts
    case SourceActionTypes.GET_SOURCE_ACCOUNTS:
      return {
        ...state,
        sourceAccountsLoading: true
      };
    case SourceActionTypes.GET_SOURCE_ACCOUNTS_SUCCESS:
      return {
        ...state,
        sourceAccounts: (action as any).payload,
        sourceAccountsLoading: false
      };

    // Update Source Account
    case SourceActionTypes.UPDATE_SOURCE_ACCOUNT:
      return {
        ...state,
        isUpdatingAccount: true
      };
    case SourceActionTypes.UPDATE_SOURCE_ACCOUNT_SUCCESS:
      return {
        ...state,
        isUpdatingAccount: false,
        sourceAccounts: (action as any).payload ?
          state.sourceAccounts.map(account =>
            account.id === (action as any).payload.id ? (action as any).payload : account
          ) : state.sourceAccounts
      };

    // Delete Source Account
    case SourceActionTypes.DELETE_SOURCE_ACCOUNT:
      return {
        ...state,
        isDeletingAccount: true
      };
    case SourceActionTypes.DELETE_SOURCE_ACCOUNT_SUCCESS:
      return {
        ...state,
        isDeletingAccount: false,
        sourceAccounts: (action as any).payload ?
          state.sourceAccounts.filter(account => account.id !== (action as any).payload.id) :
          state.sourceAccounts
      };

    // Validate Sub Source
    case SourceActionTypes.VALIDATE_SUB_SOURCE:
      return {
        ...state,
        isValidatingSubSource: true
      };
    case SourceActionTypes.VALIDATE_SUB_SOURCE_SUCCESS:
      return {
        ...state,
        subSourceValidation: { isValid: (action as any).isValid },
        isValidatingSubSource: false
      };

    default:
      return state;
  }
}

export const selectSourceState = (state: AppState) => state.source;

export const getSources = createSelector(
  selectSourceState,
  (state: SourceState) => state.sources
);

export const getSourcesLoading = createSelector(
  selectSourceState,
  (state: SourceState) => state.sourcesLoading
);

export const getSourceCountData = createSelector(
  selectSourceState,
  (state: SourceState) => state.sourceCountData
);

export const getSourceCountDataLoading = createSelector(
  selectSourceState,
  (state: SourceState) => state.sourceCountDataLoading
);

export const getIsToggleInProgress = createSelector(
  selectSourceState,
  (state: SourceState) => state.isToggleInProgress
);

export const getIsAddingSource = createSelector(
  selectSourceState,
  (state: SourceState) => state.isAddingSource
);

export const getIsUpdatingSource = createSelector(
  selectSourceState,
  (state: SourceState) => state.isUpdatingSource
);

export const getIsDeletingSource = createSelector(
  selectSourceState,
  (state: SourceState) => state.isDeletingSource
);

export const getIsConvertingToDirect = createSelector(
  selectSourceState,
  (state: SourceState) => state.isConvertingToDirect
);

export const getSourceError = createSelector(
  selectSourceState,
  (state: SourceState) => state.error
);

export const getSourceExist = createSelector(
  selectSourceState,
  (state: SourceState) => state.doesExistSourceName
);

// New selectors for account management
export const getSourceAccounts = createSelector(
  selectSourceState,
  (state: SourceState) => state.sourceAccounts
);

export const getSourceAccountsLoading = createSelector(
  selectSourceState,
  (state: SourceState) => state.sourceAccountsLoading
);

export const getIsAddingAccount = createSelector(
  selectSourceState,
  (state: SourceState) => state.isAddingAccount
);

export const getIsUpdatingAccount = createSelector(
  selectSourceState,
  (state: SourceState) => state.isUpdatingAccount
);

export const getIsDeletingAccount = createSelector(
  selectSourceState,
  (state: SourceState) => state.isDeletingAccount
);

export const getIsEnablingIntegration = createSelector(
  selectSourceState,
  (state: SourceState) => state.isEnablingIntegration
);

export const getExtractedFields = createSelector(
  selectSourceState,
  (state: SourceState) => state.extractedFields
);

export const getIsExtractingFields = createSelector(
  selectSourceState,
  (state: SourceState) => state.isExtractingFields
);

export const getSubSourceValidation = createSelector(
  selectSourceState,
  (state: SourceState) => state.subSourceValidation
);

export const getIsValidatingSubSource = createSelector(
  selectSourceState,
  (state: SourceState) => state.isValidatingSubSource
);
